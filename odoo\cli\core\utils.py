# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
CLI Utilities

This module provides common utilities for CLI commands.
"""

import logging
import os
import sys
from pathlib import Path
from typing import List, Optional, Any

import odoo
from .exceptions import ConfigurationError, ValidationError

_logger = logging.getLogger(__name__)


class CLIUtils:
    """Common utilities for CLI commands."""
    
    @staticmethod
    def setup_logging(level: str = 'INFO') -> None:
        """
        Setup logging for CLI commands.
        
        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        numeric_level = getattr(logging, level.upper(), None)
        if not isinstance(numeric_level, int):
            raise ValueError(f'Invalid log level: {level}')
        
        logging.basicConfig(
            level=numeric_level,
            format='%(asctime)s %(levelname)s %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        _logger.debug(f"Logging setup complete, level: {level}")
    
    @staticmethod
    def check_root_user() -> None:
        """Warn if the process's user is 'root' (on POSIX system)."""
        if os.name == 'posix':
            import getpass
            if getpass.getuser() == 'root':
                _logger.warning("Running as user 'root' is a security risk.")
    
    @staticmethod
    def check_postgres_user() -> None:
        """
        Exit if the configured database user is 'postgres'.
        
        This function assumes the configuration has been initialized.
        """
        config = odoo.tools.config
        if (config['db_user'] or os.environ.get('PGUSER')) == 'postgres':
            raise ConfigurationError(
                "Using the database user 'postgres' is a security risk, aborting."
            )
    
    @staticmethod
    def validate_database_name(db_name: str) -> None:
        """
        Validate database name format.
        
        Args:
            db_name: Database name to validate
            
        Raises:
            ValidationError: If database name is invalid
        """
        if not db_name:
            raise ValidationError("Database name cannot be empty")
        
        # Basic validation - can be extended
        if not db_name.replace('_', '').replace('-', '').isalnum():
            raise ValidationError(
                f"Database name '{db_name}' contains invalid characters. "
                "Use only alphanumeric characters, underscores, and hyphens."
            )
    
    @staticmethod
    def validate_path(path: str, must_exist: bool = True) -> Path:
        """
        Validate and normalize a file path.
        
        Args:
            path: Path to validate
            must_exist: Whether the path must exist
            
        Returns:
            Normalized Path object
            
        Raises:
            ValidationError: If path is invalid
        """
        if not path:
            raise ValidationError("Path cannot be empty")
        
        path_obj = Path(path).resolve()
        
        if must_exist and not path_obj.exists():
            raise ValidationError(f"Path does not exist: {path}")
        
        return path_obj
    
    @staticmethod
    def parse_comma_separated(value: str) -> List[str]:
        """
        Parse a comma-separated string into a list.
        
        Args:
            value: Comma-separated string
            
        Returns:
            List of trimmed strings
        """
        if not value:
            return []
        
        return [item.strip() for item in value.split(',') if item.strip()]
    
    @staticmethod
    def get_odoo_version() -> str:
        """Get the Odoo version string."""
        return odoo.release.version
    
    @staticmethod
    def get_python_version_info() -> str:
        """Get Python version information."""
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    @staticmethod
    def check_python_version() -> None:
        """Check if Python version is supported."""
        if sys.version_info[:2] > odoo.MAX_PY_VERSION:
            _logger.warning(
                "Python %s is not officially supported, please use Python %s instead",
                '.'.join(map(str, sys.version_info[:2])),
                '.'.join(map(str, odoo.MAX_PY_VERSION))
            )
    
    @staticmethod
    def report_configuration() -> None:
        """
        Log the server version and some configuration values.
        
        This function assumes the configuration has been initialized.
        """
        config = odoo.tools.config
        
        _logger.info("Odoo version %s", CLIUtils.get_odoo_version())
        _logger.info("Python version %s", CLIUtils.get_python_version_info())
        
        if os.path.isfile(config.rcfile):
            _logger.info("Using configuration file at %s", config.rcfile)
        
        _logger.info('addons paths: %s', odoo.addons.__path__)
        
        if config.get('upgrade_path'):
            _logger.info('upgrade path: %s', config['upgrade_path'])
        
        if config.get('pre_upgrade_scripts'):
            _logger.info('extra upgrade scripts: %s', config['pre_upgrade_scripts'])
        
        host = config['db_host'] or os.environ.get('PGHOST', 'default')
        port = config['db_port'] or os.environ.get('PGPORT', 'default')
        user = config['db_user'] or os.environ.get('PGUSER', 'default')
        _logger.info('database: %s@%s:%s', user, host, port)
        
        replica_host = config['db_replica_host']
        replica_port = config['db_replica_port']
        if replica_host is not False or replica_port:
            _logger.info('replica database: %s@%s:%s', 
                        user, replica_host or 'default', replica_port or 'default')
    
    @staticmethod
    def setup_csv_limits() -> None:
        """Setup CSV field size limits for large imports."""
        import csv
        # The default limit for CSV fields in the module is 128KiB, which is not
        # quite sufficient to import images to store in attachment. 500MiB is a
        # bit overkill, but better safe than sorry.
        csv.field_size_limit(500 * 1024 * 1024)
        _logger.debug("CSV field size limit set to 500MB")


class ConfigurationManager:
    """Manages CLI configuration parsing and validation."""
    
    @staticmethod
    def parse_config(args: List[str], setup_logging: bool = True) -> None:
        """
        Parse configuration from command line arguments.
        
        Args:
            args: Command line arguments
            setup_logging: Whether to setup logging
        """
        try:
            odoo.tools.config.parse_config(args, setup_logging=setup_logging)
        except Exception as e:
            raise ConfigurationError(f"Failed to parse configuration: {e}")
    
    @staticmethod
    def get_config_value(key: str, default: Any = None) -> Any:
        """
        Get a configuration value.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        return odoo.tools.config.get(key, default)
