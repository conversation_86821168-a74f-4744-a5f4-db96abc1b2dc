# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Odoo CLI Module

This module provides both the legacy CLI system and the new modular CLI system
with enhanced keyboard interrupt handling and better error management.
"""

import logging
import sys
import os
from typing import Optional, List

import odoo

# Import the new modular CLI system
from .core import Command
from .core.main import main as core_main

# Import enhanced commands
from .enhanced_commands import EnhancedShell, EnhancedHelp

# Import legacy commands for backward compatibility
from . import cloc
from . import upgrade_code
from . import deploy
from . import scaffold
from . import server
from . import shell
from . import start
from . import populate
from . import tsconfig
from . import neutralize
from . import obfuscate
from . import genproxytoken
from . import db

# Import refactored server
from . import server_refactored

_logger = logging.getLogger(__name__)


def main(args: Optional[List[str]] = None) -> int:
    """
    Main entry point for the Odoo CLI.
    
    This function provides a bridge between the legacy CLI system
    and the new modular CLI system.
    
    Args:
        args: Command line arguments (defaults to sys.argv[1:])
        
    Returns:
        Exit code
    """
    # Check if we should use the new CLI system
    use_new_cli = os.environ.get('ODOO_USE_NEW_CLI', '').lower() in ('1', 'true', 'yes')
    
    if use_new_cli:
        _logger.debug("Using new modular CLI system")
        return core_main(args)
    else:
        # Use legacy CLI system for backward compatibility
        _logger.debug("Using legacy CLI system")
        from .command import main as legacy_main
        return legacy_main()


# Backward compatibility exports
from .command import Command as LegacyCommand
from .command import commands as legacy_commands

__all__ = [
    'main',
    'Command',
    'LegacyCommand',
    'legacy_commands',
    'EnhancedShell',
    'EnhancedHelp',
]
