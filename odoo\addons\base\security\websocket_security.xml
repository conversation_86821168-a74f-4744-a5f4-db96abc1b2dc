<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- WebSocket Notification Access Rules -->
        <record id="websocket_notification_rule_user" model="ir.rule">
            <field name="name">WebSocket Notification: User Access</field>
            <field name="model_id" ref="model_websocket_notification"/>
            <field name="domain_force">[
                '|',
                ('target_type', '=', 'all'),
                '|',
                ('target_user_ids', 'in', [user.id]),
                ('target_group_ids', 'in', [g.id for g in user.groups_id])
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- WebSocket Notification Read Access Rules -->
        <record id="websocket_notification_read_rule_user" model="ir.rule">
            <field name="name">WebSocket Notification Read: User Access</field>
            <field name="model_id" ref="model_websocket_notification_read"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <!-- Admin can manage all notifications -->
        <record id="websocket_notification_rule_admin" model="ir.rule">
            <field name="name">WebSocket Notification: Admin Access</field>
            <field name="model_id" ref="model_websocket_notification"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
        </record>
        
        <record id="websocket_notification_read_rule_admin" model="ir.rule">
            <field name="name">WebSocket Notification Read: Admin Access</field>
            <field name="model_id" ref="model_websocket_notification_read"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
        </record>
        
    </data>
</odoo>
