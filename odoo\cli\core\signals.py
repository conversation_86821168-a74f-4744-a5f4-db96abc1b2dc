# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Signal Handling

This module provides centralized signal handling for the Odoo CLI.
"""

import os
import signal
import sys
import threading
import logging
from typing import Callable, Optional, Dict, Any

from .exceptions import KeyboardInterruptError

_logger = logging.getLogger(__name__)


class SignalHandler:
    """Centralized signal handler for CLI commands."""
    
    def __init__(self):
        self._original_handlers: Dict[int, Any] = {}
        self._cleanup_functions: list[Callable] = []
        self._interrupt_count = 0
        self._max_interrupts = 2
        self._shutdown_in_progress = False
        self._lock = threading.Lock()
    
    def register_cleanup(self, func: Callable) -> None:
        """Register a cleanup function to be called on shutdown."""
        self._cleanup_functions.append(func)
    
    def setup_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        if os.name == 'posix':
            self._setup_posix_handlers()
        elif os.name == 'nt':
            self._setup_windows_handlers()
        else:
            _logger.warning(f"Signal handling not implemented for OS: {os.name}")
    
    def _setup_posix_handlers(self) -> None:
        """Setup signal handlers for POSIX systems."""
        signals_to_handle = [
            (signal.SIGINT, self._handle_interrupt),
            (signal.SIGTERM, self._handle_terminate),
        ]
        
        # Only handle SIGHUP if it exists (not available on all systems)
        if hasattr(signal, 'SIGHUP'):
            signals_to_handle.append((signal.SIGHUP, self._handle_hangup))
        
        for sig, handler in signals_to_handle:
            try:
                self._original_handlers[sig] = signal.signal(sig, handler)
                _logger.debug(f"Registered signal handler for {sig}")
            except (OSError, ValueError) as e:
                _logger.warning(f"Could not register handler for signal {sig}: {e}")
    
    def _setup_windows_handlers(self) -> None:
        """Setup signal handlers for Windows systems."""
        try:
            import win32api
            win32api.SetConsoleCtrlHandler(self._handle_windows_signal, True)
            _logger.debug("Registered Windows console control handler")
        except ImportError:
            _logger.warning("win32api not available, limited signal handling on Windows")
            # Fallback to basic signal handling
            try:
                self._original_handlers[signal.SIGINT] = signal.signal(signal.SIGINT, self._handle_interrupt)
            except (OSError, ValueError) as e:
                _logger.warning(f"Could not register SIGINT handler: {e}")
    
    def _handle_interrupt(self, signum: int, frame) -> None:
        """Handle SIGINT (Ctrl+C)."""
        with self._lock:
            self._interrupt_count += 1
            
            if self._shutdown_in_progress:
                if self._interrupt_count >= self._max_interrupts:
                    _logger.error("Force shutdown requested")
                    os._exit(130)  # Standard exit code for SIGINT
                return
            
            if self._interrupt_count == 1:
                _logger.info("Interrupt received, initiating graceful shutdown...")
                self._shutdown_in_progress = True
                self._graceful_shutdown()
            else:
                _logger.warning(f"Interrupt received again ({self._interrupt_count}/{self._max_interrupts}), forcing shutdown...")
                if self._interrupt_count >= self._max_interrupts:
                    _logger.error("Maximum interrupts reached, forcing immediate exit")
                    os._exit(130)
    
    def _handle_terminate(self, signum: int, frame) -> None:
        """Handle SIGTERM."""
        _logger.info("Terminate signal received, shutting down...")
        self._graceful_shutdown()
    
    def _handle_hangup(self, signum: int, frame) -> None:
        """Handle SIGHUP."""
        _logger.info("Hangup signal received, shutting down...")
        self._graceful_shutdown()
    
    def _handle_windows_signal(self, signal_type) -> bool:
        """Handle Windows console control signals."""
        if signal_type in (0, 2):  # CTRL_C_EVENT, CTRL_CLOSE_EVENT
            self._handle_interrupt(signal.SIGINT, None)
            return True
        return False
    
    def _graceful_shutdown(self) -> None:
        """Perform graceful shutdown."""
        try:
            _logger.debug("Running cleanup functions...")
            for cleanup_func in self._cleanup_functions:
                try:
                    cleanup_func()
                except Exception as e:
                    _logger.error(f"Error in cleanup function {cleanup_func}: {e}")
            
            _logger.info("Graceful shutdown completed")
            
        except Exception as e:
            _logger.error(f"Error during graceful shutdown: {e}")
        finally:
            # Raise KeyboardInterrupt to allow normal exception handling
            raise KeyboardInterrupt()
    
    def restore_handlers(self) -> None:
        """Restore original signal handlers."""
        for sig, original_handler in self._original_handlers.items():
            try:
                signal.signal(sig, original_handler)
                _logger.debug(f"Restored original handler for signal {sig}")
            except (OSError, ValueError) as e:
                _logger.warning(f"Could not restore handler for signal {sig}: {e}")
        
        self._original_handlers.clear()


# Global signal handler instance
_signal_handler: Optional[SignalHandler] = None


def get_signal_handler() -> SignalHandler:
    """Get the global signal handler instance."""
    global _signal_handler
    if _signal_handler is None:
        _signal_handler = SignalHandler()
    return _signal_handler


def setup_default_signal_handlers() -> None:
    """Setup default signal handlers for CLI commands."""
    handler = get_signal_handler()
    handler.setup_handlers()


def register_cleanup_function(func: Callable) -> None:
    """Register a cleanup function to be called on shutdown."""
    handler = get_signal_handler()
    handler.register_cleanup(func)


def restore_signal_handlers() -> None:
    """Restore original signal handlers."""
    global _signal_handler
    if _signal_handler:
        _signal_handler.restore_handlers()
        _signal_handler = None
