# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
New Modular Command System

This module provides the new modular command system for Odoo CLI
with enhanced signal handling and exception management.
"""

import sys
from typing import Optional, List

from .core.main import main as core_main


def main(args: Optional[List[str]] = None) -> int:
    """
    Main entry point for the Odoo CLI.
    
    This function provides backward compatibility while using
    the new modular CLI system.
    
    Args:
        args: Command line arguments (defaults to sys.argv[1:])
        
    Returns:
        Exit code
    """
    return core_main(args)


if __name__ == "__main__":
    sys.exit(main())
