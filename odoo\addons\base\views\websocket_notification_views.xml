<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- WebSocket Notification Views -->
        <record id="view_websocket_notification_tree" model="ir.ui.view">
            <field name="name">websocket.notification.tree</field>
            <field name="model">websocket.notification</field>
            <field name="arch" type="xml">
                <tree string="WebSocket Notifications" default_order="create_date desc">
                    <field name="title"/>
                    <field name="notification_type"/>
                    <field name="target_type"/>
                    <field name="priority"/>
                    <field name="state"/>
                    <field name="sent_date"/>
                    <field name="delivery_count"/>
                    <field name="read_count"/>
                    <field name="create_date"/>
                </tree>
            </field>
        </record>
        
        <record id="view_websocket_notification_form" model="ir.ui.view">
            <field name="name">websocket.notification.form</field>
            <field name="model">websocket.notification</field>
            <field name="arch" type="xml">
                <form string="WebSocket Notification">
                    <header>
                        <button name="send_notification" type="object" string="Send Notification" 
                                class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,sent,failed"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="title"/>
                                <field name="notification_type"/>
                                <field name="priority"/>
                                <field name="target_type"/>
                            </group>
                            <group>
                                <field name="sent_date" readonly="1"/>
                                <field name="delivery_count" readonly="1"/>
                                <field name="read_count" readonly="1"/>
                                <field name="expires_at"/>
                                <field name="auto_delete"/>
                            </group>
                        </group>
                        
                        <group string="Target Configuration" attrs="{'invisible': [('target_type', '=', 'all')]}">
                            <field name="target_user_ids" widget="many2many_tags" 
                                   attrs="{'invisible': [('target_type', '!=', 'user')], 'required': [('target_type', '=', 'user')]}"/>
                            <field name="target_group_ids" widget="many2many_tags"
                                   attrs="{'invisible': [('target_type', '!=', 'group')], 'required': [('target_type', '=', 'group')]}"/>
                            <field name="channel_name" 
                                   attrs="{'invisible': [('target_type', '!=', 'channel')], 'required': [('target_type', '=', 'channel')]}"/>
                        </group>
                        
                        <group string="Message">
                            <field name="message" widget="text"/>
                        </group>
                        
                        <group string="Additional Data (JSON)">
                            <field name="data" widget="text"/>
                        </group>
                        
                        <group string="Error Information" attrs="{'invisible': [('state', '!=', 'failed')]}">
                            <field name="error_message" readonly="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <record id="view_websocket_notification_search" model="ir.ui.view">
            <field name="name">websocket.notification.search</field>
            <field name="model">websocket.notification</field>
            <field name="arch" type="xml">
                <search string="WebSocket Notifications">
                    <field name="title"/>
                    <field name="message"/>
                    <field name="target_user_ids"/>
                    <field name="target_group_ids"/>
                    <field name="channel_name"/>
                    
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="sent" string="Sent" domain="[('state', '=', 'sent')]"/>
                    <filter name="failed" string="Failed" domain="[('state', '=', 'failed')]"/>
                    
                    <separator/>
                    <filter name="info" string="Info" domain="[('notification_type', '=', 'info')]"/>
                    <filter name="warning" string="Warning" domain="[('notification_type', '=', 'warning')]"/>
                    <filter name="error" string="Error" domain="[('notification_type', '=', 'error')]"/>
                    <filter name="success" string="Success" domain="[('notification_type', '=', 'success')]"/>
                    
                    <separator/>
                    <filter name="high_priority" string="High Priority" domain="[('priority', 'in', ['high', 'urgent'])]"/>
                    
                    <group expand="0" string="Group By">
                        <filter name="group_by_type" string="Type" context="{'group_by': 'notification_type'}"/>
                        <filter name="group_by_target" string="Target Type" context="{'group_by': 'target_type'}"/>
                        <filter name="group_by_state" string="State" context="{'group_by': 'state'}"/>
                        <filter name="group_by_priority" string="Priority" context="{'group_by': 'priority'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- WebSocket Notification Read Views -->
        <record id="view_websocket_notification_read_tree" model="ir.ui.view">
            <field name="name">websocket.notification.read.tree</field>
            <field name="model">websocket.notification.read</field>
            <field name="arch" type="xml">
                <tree string="Notification Read Status" create="false" edit="false">
                    <field name="notification_id"/>
                    <field name="user_id"/>
                    <field name="read_date"/>
                </tree>
            </field>
        </record>
        
        <!-- Actions -->
        <record id="action_websocket_notification" model="ir.actions.act_window">
            <field name="name">WebSocket Notifications</field>
            <field name="res_model">websocket.notification</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WebSocket notification!
                </p>
                <p>
                    WebSocket notifications allow you to send real-time messages to users
                    connected via WebSocket connections.
                </p>
            </field>
        </record>
        
        <record id="action_websocket_notification_read" model="ir.actions.act_window">
            <field name="name">Notification Read Status</field>
            <field name="res_model">websocket.notification.read</field>
            <field name="view_mode">tree</field>
            <field name="context">{}</field>
        </record>
        
        <!-- Menu Items -->
        <menuitem id="menu_websocket_notifications_root" 
                  name="WebSocket Notifications" 
                  parent="base.menu_administration"
                  groups="base.group_system"
                  sequence="100"/>
        
        <menuitem id="menu_websocket_notifications" 
                  name="Notifications" 
                  parent="menu_websocket_notifications_root"
                  action="action_websocket_notification"
                  sequence="10"/>
        
        <menuitem id="menu_websocket_notification_reads" 
                  name="Read Status" 
                  parent="menu_websocket_notifications_root"
                  action="action_websocket_notification_read"
                  sequence="20"/>
        
    </data>
</odoo>
