# Part of Odoo. See LICENSE file for full copyright and licensing details.

import json
import logging
import asyncio
from typing import Dict, Set, Optional, Any
from datetime import datetime

from starlette.websockets import WebSocket, WebSocketDisconnect
from starlette.endpoints import WebSocketEndpoint

import odoo
from odoo import http, api, registry
from odoo.http import request
from odoo.exceptions import AccessError, UserError
from odoo.tools import config

_logger = logging.getLogger(__name__)


class WebSocketConnectionManager:
    """
    Manages WebSocket connections for real-time communication
    """
    
    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[int, Set[WebSocket]] = {}
        # Store connection metadata
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
    
    async def connect(self, websocket: WebSocket, user_id: int, db_name: str, session_id: str = None):
        """Accept a WebSocket connection and register it"""
        await websocket.accept()
        
        async with self._lock:
            if user_id not in self.active_connections:
                self.active_connections[user_id] = set()
            
            self.active_connections[user_id].add(websocket)
            self.connection_metadata[websocket] = {
                'user_id': user_id,
                'db_name': db_name,
                'session_id': session_id,
                'connected_at': datetime.now(),
                'last_activity': datetime.now()
            }
        
        _logger.info(f"WebSocket connected for user {user_id} on database {db_name}")
    
    async def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        async with self._lock:
            metadata = self.connection_metadata.get(websocket)
            if metadata:
                user_id = metadata['user_id']
                if user_id in self.active_connections:
                    self.active_connections[user_id].discard(websocket)
                    if not self.active_connections[user_id]:
                        del self.active_connections[user_id]
                
                del self.connection_metadata[websocket]
                _logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def send_personal_message(self, message: str, user_id: int):
        """Send a message to all connections of a specific user"""
        if user_id in self.active_connections:
            connections = self.active_connections[user_id].copy()
            for connection in connections:
                try:
                    await connection.send_text(message)
                    # Update last activity
                    if connection in self.connection_metadata:
                        self.connection_metadata[connection]['last_activity'] = datetime.now()
                except Exception as e:
                    _logger.warning(f"Failed to send message to user {user_id}: {e}")
                    await self.disconnect(connection)
    
    async def broadcast(self, message: str, db_name: str = None):
        """Broadcast a message to all connected users, optionally filtered by database"""
        connections_to_send = []
        
        async with self._lock:
            for connections in self.active_connections.values():
                for connection in connections:
                    metadata = self.connection_metadata.get(connection)
                    if metadata and (db_name is None or metadata['db_name'] == db_name):
                        connections_to_send.append(connection)
        
        for connection in connections_to_send:
            try:
                await connection.send_text(message)
                # Update last activity
                if connection in self.connection_metadata:
                    self.connection_metadata[connection]['last_activity'] = datetime.now()
            except Exception as e:
                _logger.warning(f"Failed to broadcast message: {e}")
                await self.disconnect(connection)
    
    def get_user_connections_count(self, user_id: int) -> int:
        """Get the number of active connections for a user"""
        return len(self.active_connections.get(user_id, set()))
    
    def get_total_connections_count(self) -> int:
        """Get the total number of active connections"""
        return sum(len(connections) for connections in self.active_connections.values())


# Global connection manager instance
connection_manager = WebSocketConnectionManager()


class WebSocketController(http.Controller):
    """
    WebSocket controller for handling real-time communication
    """
    
    def _authenticate_websocket(self, websocket: WebSocket) -> tuple:
        """
        Authenticate WebSocket connection using enhanced authentication service
        Returns (user_id, db_name, session_id) or raises exception
        """
        try:
            from odoo.service.websocket_auth import get_websocket_authenticator
            authenticator = get_websocket_authenticator()
            return authenticator.authenticate_websocket(websocket)
        except Exception as e:
            _logger.warning(f"WebSocket authentication failed: {e}")
            raise AccessError(f"Authentication failed: {e}")
    
    async def _handle_websocket_message(self, websocket: WebSocket, message: dict, user_id: int, db_name: str):
        """
        Handle incoming WebSocket messages
        """
        message_type = message.get('type')
        
        if message_type == 'ping':
            # Respond to ping with pong
            await websocket.send_text(json.dumps({
                'type': 'pong',
                'timestamp': datetime.now().isoformat()
            }))
        
        elif message_type == 'subscribe':
            # Handle subscription to specific channels/events
            channel = message.get('channel')
            if channel:
                # Store subscription info in connection metadata
                metadata = connection_manager.connection_metadata.get(websocket, {})
                if 'subscriptions' not in metadata:
                    metadata['subscriptions'] = set()
                metadata['subscriptions'].add(channel)
                
                await websocket.send_text(json.dumps({
                    'type': 'subscribed',
                    'channel': channel
                }))
        
        elif message_type == 'unsubscribe':
            # Handle unsubscription from channels/events
            channel = message.get('channel')
            if channel:
                metadata = connection_manager.connection_metadata.get(websocket, {})
                if 'subscriptions' in metadata:
                    metadata['subscriptions'].discard(channel)
                
                await websocket.send_text(json.dumps({
                    'type': 'unsubscribed',
                    'channel': channel
                }))
        
        elif message_type == 'notification_read':
            # Handle notification read acknowledgment
            notification_id = message.get('notification_id')
            if notification_id:
                # Mark notification as read in database
                try:
                    with registry(db_name).cursor() as cr:
                        env = api.Environment(cr, user_id, {})
                        # This would integrate with Odoo's notification system
                        # env['mail.notification'].browse(notification_id).mark_read()
                        pass
                except Exception as e:
                    _logger.error(f"Failed to mark notification as read: {e}")
        
        else:
            _logger.warning(f"Unknown WebSocket message type: {message_type}")


class OdooWebSocketEndpoint(WebSocketEndpoint):
    """
    ASGI WebSocket endpoint for Odoo
    """
    
    async def on_connect(self, websocket: WebSocket):
        """Handle WebSocket connection"""
        try:
            controller = WebSocketController()
            user_id, db_name, session_id = controller._authenticate_websocket(websocket)
            await connection_manager.connect(websocket, user_id, db_name, session_id)
            
            # Send welcome message
            await websocket.send_text(json.dumps({
                'type': 'welcome',
                'user_id': user_id,
                'db_name': db_name,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            _logger.error(f"WebSocket connection failed: {e}")
            await websocket.close(code=1008, reason=str(e))
    
    async def on_receive(self, websocket: WebSocket, data):
        """Handle incoming WebSocket messages"""
        try:
            metadata = connection_manager.connection_metadata.get(websocket)
            if not metadata:
                await websocket.close(code=1008, reason="Connection not authenticated")
                return
            
            user_id = metadata['user_id']
            db_name = metadata['db_name']
            
            # Parse message
            if isinstance(data, str):
                message = json.loads(data)
            else:
                message = data
            
            controller = WebSocketController()
            await controller._handle_websocket_message(websocket, message, user_id, db_name)
            
        except json.JSONDecodeError:
            await websocket.send_text(json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
        except Exception as e:
            _logger.error(f"Error handling WebSocket message: {e}")
            await websocket.send_text(json.dumps({
                'type': 'error',
                'message': 'Internal server error'
            }))
    
    async def on_disconnect(self, websocket: WebSocket, close_code):
        """Handle WebSocket disconnection"""
        await connection_manager.disconnect(websocket)


# Export the connection manager for use by other modules
__all__ = ['connection_manager', 'WebSocketController', 'OdooWebSocketEndpoint']
