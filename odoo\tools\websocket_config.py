# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Configuration for Odoo

This module provides configuration options for WebSocket functionality,
including connection limits, authentication settings, and performance tuning.
"""

import logging
from odoo.tools import config

_logger = logging.getLogger(__name__)


class WebSocketConfig:
    """
    WebSocket configuration manager
    """
    
    def __init__(self):
        self._config = {}
        self._load_config()
    
    def _load_config(self):
        """Load WebSocket configuration from Odoo config"""
        
        # WebSocket Server Configuration
        self._config.update({
            # Enable/disable WebSocket functionality
            'websocket_enabled': config.get('websocket_enabled', True),
            
            # WebSocket server settings
            'websocket_host': config.get('websocket_host', '0.0.0.0'),
            'websocket_port': config.get('websocket_port', None),  # Use same port as HTTP by default
            'websocket_path': config.get('websocket_path', '/websocket'),
            
            # Connection limits
            'websocket_max_connections': config.get('websocket_max_connections', 1000),
            'websocket_max_connections_per_user': config.get('websocket_max_connections_per_user', 10),
            'websocket_max_connections_per_ip': config.get('websocket_max_connections_per_ip', 50),
            
            # Message limits
            'websocket_max_message_size': config.get('websocket_max_message_size', 1024 * 1024),  # 1MB
            'websocket_max_messages_per_second': config.get('websocket_max_messages_per_second', 100),
            
            # Timeout settings
            'websocket_connection_timeout': config.get('websocket_connection_timeout', 60),
            'websocket_idle_timeout': config.get('websocket_idle_timeout', 1800),  # 30 minutes
            'websocket_ping_interval': config.get('websocket_ping_interval', 30),
            'websocket_ping_timeout': config.get('websocket_ping_timeout', 10),
            
            # Authentication settings
            'websocket_auth_required': config.get('websocket_auth_required', True),
            'websocket_session_auth': config.get('websocket_session_auth', True),
            'websocket_token_auth': config.get('websocket_token_auth', True),
            'websocket_api_key_auth': config.get('websocket_api_key_auth', False),
            'websocket_token_expiry': config.get('websocket_token_expiry', 3600),  # 1 hour
            'websocket_secret_key': config.get('websocket_secret_key', None),
            
            # Rate limiting
            'websocket_rate_limit_enabled': config.get('websocket_rate_limit_enabled', True),
            'websocket_rate_limit_requests': config.get('websocket_rate_limit_requests', 100),
            'websocket_rate_limit_window': config.get('websocket_rate_limit_window', 60),
            'websocket_rate_limit_lockout': config.get('websocket_rate_limit_lockout', 300),
            
            # Compression settings
            'websocket_compression_enabled': config.get('websocket_compression_enabled', True),
            'websocket_compression_level': config.get('websocket_compression_level', 6),
            'websocket_compression_threshold': config.get('websocket_compression_threshold', 1024),
            
            # Logging and monitoring
            'websocket_log_level': config.get('websocket_log_level', 'INFO'),
            'websocket_log_connections': config.get('websocket_log_connections', True),
            'websocket_log_messages': config.get('websocket_log_messages', False),
            'websocket_metrics_enabled': config.get('websocket_metrics_enabled', True),
            
            # Notification settings
            'websocket_notifications_enabled': config.get('websocket_notifications_enabled', True),
            'websocket_notification_queue_size': config.get('websocket_notification_queue_size', 10000),
            'websocket_notification_batch_size': config.get('websocket_notification_batch_size', 100),
            'websocket_notification_retry_attempts': config.get('websocket_notification_retry_attempts', 3),
            
            # Cleanup settings
            'websocket_cleanup_enabled': config.get('websocket_cleanup_enabled', True),
            'websocket_cleanup_interval': config.get('websocket_cleanup_interval', 300),  # 5 minutes
            'websocket_cleanup_max_idle': config.get('websocket_cleanup_max_idle', 1800),  # 30 minutes
            
            # Performance settings
            'websocket_worker_threads': config.get('websocket_worker_threads', 4),
            'websocket_buffer_size': config.get('websocket_buffer_size', 65536),  # 64KB
            'websocket_backlog': config.get('websocket_backlog', 100),
            
            # Security settings
            'websocket_cors_enabled': config.get('websocket_cors_enabled', True),
            'websocket_cors_origins': config.get('websocket_cors_origins', '*'),
            'websocket_ssl_enabled': config.get('websocket_ssl_enabled', False),
            'websocket_ssl_cert': config.get('websocket_ssl_cert', None),
            'websocket_ssl_key': config.get('websocket_ssl_key', None),
            
            # Development settings
            'websocket_debug': config.get('websocket_debug', False),
            'websocket_auto_reload': config.get('websocket_auto_reload', False),
        })
    
    def get(self, key, default=None):
        """Get a configuration value"""
        return self._config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value (runtime only)"""
        self._config[key] = value
    
    def is_enabled(self):
        """Check if WebSocket functionality is enabled"""
        return self.get('websocket_enabled', True)
    
    def get_connection_limits(self):
        """Get connection limit settings"""
        return {
            'max_connections': self.get('websocket_max_connections'),
            'max_connections_per_user': self.get('websocket_max_connections_per_user'),
            'max_connections_per_ip': self.get('websocket_max_connections_per_ip'),
        }
    
    def get_timeout_settings(self):
        """Get timeout settings"""
        return {
            'connection_timeout': self.get('websocket_connection_timeout'),
            'idle_timeout': self.get('websocket_idle_timeout'),
            'ping_interval': self.get('websocket_ping_interval'),
            'ping_timeout': self.get('websocket_ping_timeout'),
        }
    
    def get_auth_settings(self):
        """Get authentication settings"""
        return {
            'auth_required': self.get('websocket_auth_required'),
            'session_auth': self.get('websocket_session_auth'),
            'token_auth': self.get('websocket_token_auth'),
            'api_key_auth': self.get('websocket_api_key_auth'),
            'token_expiry': self.get('websocket_token_expiry'),
            'secret_key': self.get('websocket_secret_key'),
        }
    
    def get_rate_limit_settings(self):
        """Get rate limiting settings"""
        return {
            'enabled': self.get('websocket_rate_limit_enabled'),
            'requests': self.get('websocket_rate_limit_requests'),
            'window': self.get('websocket_rate_limit_window'),
            'lockout': self.get('websocket_rate_limit_lockout'),
        }
    
    def get_compression_settings(self):
        """Get compression settings"""
        return {
            'enabled': self.get('websocket_compression_enabled'),
            'level': self.get('websocket_compression_level'),
            'threshold': self.get('websocket_compression_threshold'),
        }
    
    def get_notification_settings(self):
        """Get notification settings"""
        return {
            'enabled': self.get('websocket_notifications_enabled'),
            'queue_size': self.get('websocket_notification_queue_size'),
            'batch_size': self.get('websocket_notification_batch_size'),
            'retry_attempts': self.get('websocket_notification_retry_attempts'),
        }
    
    def validate_config(self):
        """Validate WebSocket configuration"""
        errors = []
        warnings = []
        
        # Check required settings
        if self.get('websocket_enabled') and not self.get('websocket_auth_required'):
            warnings.append("WebSocket authentication is disabled - this may be a security risk")
        
        # Check numeric ranges
        max_connections = self.get('websocket_max_connections')
        if max_connections <= 0:
            errors.append("websocket_max_connections must be greater than 0")
        
        max_message_size = self.get('websocket_max_message_size')
        if max_message_size <= 0:
            errors.append("websocket_max_message_size must be greater than 0")
        
        # Check timeout values
        connection_timeout = self.get('websocket_connection_timeout')
        if connection_timeout <= 0:
            errors.append("websocket_connection_timeout must be greater than 0")
        
        idle_timeout = self.get('websocket_idle_timeout')
        if idle_timeout <= 0:
            errors.append("websocket_idle_timeout must be greater than 0")
        
        # Check compression level
        compression_level = self.get('websocket_compression_level')
        if not (1 <= compression_level <= 9):
            errors.append("websocket_compression_level must be between 1 and 9")
        
        # Check SSL settings
        if self.get('websocket_ssl_enabled'):
            if not self.get('websocket_ssl_cert'):
                errors.append("websocket_ssl_cert is required when SSL is enabled")
            if not self.get('websocket_ssl_key'):
                errors.append("websocket_ssl_key is required when SSL is enabled")
        
        # Log validation results
        if errors:
            for error in errors:
                _logger.error(f"WebSocket configuration error: {error}")
        
        if warnings:
            for warning in warnings:
                _logger.warning(f"WebSocket configuration warning: {warning}")
        
        return len(errors) == 0, errors, warnings
    
    def get_uvicorn_config(self):
        """Get configuration for Uvicorn WebSocket server"""
        return {
            'host': self.get('websocket_host'),
            'port': self.get('websocket_port'),
            'ws': 'websockets',  # Use websockets implementation
            'ws_max_size': self.get('websocket_max_message_size'),
            'ws_ping_interval': self.get('websocket_ping_interval'),
            'ws_ping_timeout': self.get('websocket_ping_timeout'),
            'timeout_keep_alive': self.get('websocket_idle_timeout'),
            'backlog': self.get('websocket_backlog'),
        }
    
    def __str__(self):
        """String representation of configuration"""
        enabled = "enabled" if self.is_enabled() else "disabled"
        return f"WebSocketConfig({enabled}, {len(self._config)} settings)"


# Global configuration instance
websocket_config = WebSocketConfig()


def get_websocket_config():
    """Get the global WebSocket configuration"""
    return websocket_config


def validate_websocket_config():
    """Validate WebSocket configuration and log results"""
    config = get_websocket_config()
    is_valid, errors, warnings = config.validate_config()
    
    if not is_valid:
        _logger.error("WebSocket configuration validation failed")
        for error in errors:
            _logger.error(f"  - {error}")
    else:
        _logger.info("WebSocket configuration validation passed")
        if warnings:
            for warning in warnings:
                _logger.warning(f"  - {warning}")
    
    return is_valid
