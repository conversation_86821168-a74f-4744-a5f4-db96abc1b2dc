# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Main CLI Entry Point

This module provides the main entry point for the Odoo CLI with enhanced
signal handling, exception handling, and command routing.
"""

import logging
import sys
from typing import List, Optional

from .registry import commands
from .discovery import CommandDiscovery
from .help import HelpCommand
from .exception_handler import setup_exception_handling, handle_cli_exception
from .signals import setup_default_signal_handlers, restore_signal_handlers
from .exceptions import CommandNotFoundError, CLIError

_logger = logging.getLogger(__name__)


class CLIMain:
    """Main CLI application class."""
    
    def __init__(self, debug: bool = False, verbose: bool = False):
        self.debug = debug
        self.verbose = verbose
        self._exception_handler = None
    
    def setup(self):
        """Setup the CLI environment."""
        # Setup exception handling
        self._exception_handler = setup_exception_handling(
            debug=self.debug, 
            verbose=self.verbose
        )
        
        # Setup signal handlers
        setup_default_signal_handlers()
        
        _logger.debug("CLI setup completed")
    
    def discover_commands(self, args: List[str]) -> List[str]:
        """
        Discover addon commands and parse addons-path argument.
        
        Args:
            args: Command line arguments
            
        Returns:
            Remaining arguments after processing addons-path
        """
        # Parse addons-path argument
        remaining_args, addons_path_found = CommandDiscovery.parse_addons_path_arg(args)
        
        # Discover addon commands if we have arguments and the first isn't a flag
        if remaining_args and not remaining_args[0].startswith("-"):
            CommandDiscovery.discover_addon_commands()
        
        return remaining_args
    
    def parse_command(self, args: List[str]) -> tuple[str, List[str]]:
        """
        Parse command name and arguments.
        
        Args:
            args: Command line arguments
            
        Returns:
            Tuple of (command_name, command_args)
        """
        # Default to server command for backward compatibility
        command_name = "server"
        command_args = args
        
        # If we have arguments and the first isn't a flag, it's a command
        if args and not args[0].startswith("-"):
            command_name = args[0]
            command_args = args[1:]
        
        return command_name, command_args
    
    def execute_command(self, command_name: str, command_args: List[str]) -> int:
        """
        Execute a command.
        
        Args:
            command_name: Name of the command to execute
            command_args: Arguments for the command
            
        Returns:
            Exit code
        """
        try:
            # Get command class
            command_class = commands.get(command_name)
            
            # Create and execute command instance
            command_instance = command_class()
            command_instance.execute(command_args)
            
            return 0
            
        except CommandNotFoundError as e:
            if self.verbose:
                _logger.error("Command not found: %s", e.message)
            raise
        except Exception as e:
            _logger.error("Error executing command '%s': %s", command_name, e)
            raise
    
    def run(self, args: Optional[List[str]] = None) -> int:
        """
        Run the CLI application.
        
        Args:
            args: Command line arguments (defaults to sys.argv[1:])
            
        Returns:
            Exit code
        """
        if args is None:
            args = sys.argv[1:]
        
        try:
            # Setup CLI environment
            self.setup()
            
            # Discover commands
            remaining_args = self.discover_commands(args)
            
            # Parse command and arguments
            command_name, command_args = self.parse_command(remaining_args)
            
            # Execute command
            return self.execute_command(command_name, command_args)
            
        except KeyboardInterrupt:
            _logger.info("Operation interrupted by user")
            return 130
        except CLIError as e:
            handle_cli_exception(e, debug=self.debug, verbose=self.verbose)
        except Exception as e:
            handle_cli_exception(e, debug=self.debug, verbose=self.verbose)
        finally:
            # Cleanup
            self.cleanup()
    
    def cleanup(self):
        """Cleanup CLI resources."""
        try:
            restore_signal_handlers()
        except Exception as e:
            _logger.debug("Error during cleanup: %s", e)


def main(args: Optional[List[str]] = None) -> int:
    """
    Main entry point for the Odoo CLI.
    
    Args:
        args: Command line arguments (defaults to sys.argv[1:])
        
    Returns:
        Exit code
    """
    # Check for debug/verbose flags
    if args is None:
        args = sys.argv[1:]
    
    debug = any(arg in ['--debug'] for arg in args)
    verbose = any(arg in ['--verbose', '-v'] for arg in args)
    
    # Create and run CLI application
    cli = CLIMain(debug=debug, verbose=verbose)
    return cli.run(args)


if __name__ == "__main__":
    sys.exit(main())
