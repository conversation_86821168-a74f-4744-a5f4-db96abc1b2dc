# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
CLI Exception Classes

This module defines custom exceptions for the Odoo CLI system.
"""

import sys


class CLIError(Exception):
    """Base exception for CLI-related errors."""
    
    def __init__(self, message, exit_code=1):
        super().__init__(message)
        self.exit_code = exit_code
        self.message = message


class CommandNotFoundError(CLIError):
    """Raised when a requested command is not found."""
    
    def __init__(self, command_name, available_commands=None):
        self.command_name = command_name
        self.available_commands = available_commands or []
        
        message = f"Unknown command '{command_name}'"
        if self.available_commands:
            message += f". Available commands: {', '.join(sorted(self.available_commands))}"
        
        super().__init__(message)


class KeyboardInterruptError(CLIError):
    """Raised when a keyboard interrupt is received."""
    
    def __init__(self, message="Operation interrupted by user"):
        super().__init__(message, exit_code=130)  # Standard exit code for SIGINT


class ConfigurationError(CLIError):
    """Raised when there's a configuration-related error."""
    
    def __init__(self, message):
        super().__init__(f"Configuration error: {message}")


class ValidationError(CLIError):
    """Raised when command arguments or options fail validation."""
    
    def __init__(self, message):
        super().__init__(f"Validation error: {message}")


def handle_cli_exception(exc, debug=False):
    """
    Handle CLI exceptions with appropriate error messages and exit codes.
    
    Args:
        exc: The exception to handle
        debug: Whether to show debug information (stack traces)
    """
    if isinstance(exc, KeyboardInterrupt):
        exc = KeyboardInterruptError()
    
    if isinstance(exc, CLIError):
        if debug:
            import traceback
            traceback.print_exc()
        else:
            print(f"Error: {exc.message}", file=sys.stderr)
        sys.exit(exc.exit_code)
    else:
        # Unexpected error
        if debug:
            import traceback
            traceback.print_exc()
        else:
            print(f"Unexpected error: {exc}", file=sys.stderr)
        sys.exit(1)
