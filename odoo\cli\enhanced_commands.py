# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Enhanced CLI Commands

This module provides enhanced versions of CLI commands with proper
keyboard interrupt handling and modular structure.
"""

import code
import logging
import os
import signal
import sys
import threading
from pathlib import Path
from typing import Optional

import odoo
from odoo.modules.registry import Registry
from odoo.tools import config

from .core import Command
from .core.utils import CLIUtils, ConfigurationManager
from .core.signals import register_cleanup_function

_logger = logging.getLogger(__name__)


def raise_keyboard_interrupt(*a):
    """Signal handler that raises KeyboardInterrupt."""
    raise KeyboardInterrupt()


class Console(code.InteractiveConsole):
    """Enhanced interactive console with better error handling."""
    
    def __init__(self, locals=None, filename="<console>"):
        code.InteractiveConsole.__init__(self, locals, filename)
        self._setup_readline()
    
    def _setup_readline(self):
        """Setup readline for autocomplete if available."""
        try:
            import readline
            import rlcompleter
        except ImportError:
            print('readline or rlcompleter not available, autocomplete disabled.')
        else:
            readline.set_completer(rlcompleter.Completer(self.locals).complete)
            readline.parse_and_bind("tab: complete")
    
    def interact(self, banner=None, exitmsg=None):
        """Enhanced interact method with keyboard interrupt handling."""
        try:
            super().interact(banner, exitmsg)
        except KeyboardInterrupt:
            print("\nKeyboardInterrupt")
        except EOFError:
            print("\nExiting...")


class EnhancedShell(Command):
    """Start odoo in an interactive shell with enhanced keyboard interrupt handling"""
    
    name = "shell"
    supported_shells = ['ipython', 'ptpython', 'bpython', 'python']
    
    def __init__(self):
        super().__init__()
        self._registry = None
        self._cursor = None
    
    def init(self, args):
        """Initialize the shell environment."""
        config.parser.prog = self._get_prog_name()
        
        # Parse configuration
        ConfigurationManager.parse_config(args, setup_logging=True)
        
        # Report configuration
        CLIUtils.report_configuration()
        
        # Start server in stop mode (no HTTP server)
        odoo.service.server.start(preload=[], stop=True)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, raise_keyboard_interrupt)
        
        # Register cleanup
        register_cleanup_function(self._cleanup_database_resources)
    
    def _cleanup_database_resources(self):
        """Cleanup database resources."""
        if self._cursor:
            try:
                self._cursor.rollback()
                self._cursor.close()
            except Exception as e:
                _logger.debug("Error cleaning up cursor: %s", e)
        
        if self._registry:
            try:
                # Registry cleanup if needed
                pass
            except Exception as e:
                _logger.debug("Error cleaning up registry: %s", e)
    
    def console(self, local_vars):
        """Start the appropriate console interface."""
        if not os.isatty(sys.stdin.fileno()):
            # Non-interactive mode
            local_vars['__name__'] = '__main__'
            try:
                exec(sys.stdin.read(), local_vars)
            except KeyboardInterrupt:
                _logger.info("Script execution interrupted")
                return
        else:
            # Interactive mode
            if 'env' not in local_vars:
                print(f'No environment set, use `{sys.argv[0]} shell -d dbname` to get one.')
            
            # Show available variables
            for i in sorted(local_vars):
                print(f'{i}: {local_vars[i]}')
            
            # Try shells in order of preference
            preferred_interface = config.options.get('shell_interface')
            if preferred_interface:
                shells_to_try = [preferred_interface, 'python']
            else:
                shells_to_try = self.supported_shells
            
            for shell in shells_to_try:
                try:
                    return getattr(self, shell)(local_vars)
                except ImportError:
                    continue
                except KeyboardInterrupt:
                    _logger.info("Shell interrupted")
                    return
                except Exception:
                    _logger.warning("Could not start '%s' shell.", shell)
                    _logger.debug("Shell error:", exc_info=True)
    
    def ipython(self, local_vars):
        """Start IPython shell."""
        try:
            from IPython import start_ipython
            start_ipython(argv=[], user_ns=local_vars)
        except KeyboardInterrupt:
            print("\nIPython session interrupted")
    
    def ptpython(self, local_vars):
        """Start ptpython shell."""
        try:
            from ptpython.repl import embed
            embed({}, local_vars)
        except KeyboardInterrupt:
            print("\nptpython session interrupted")
    
    def bpython(self, local_vars):
        """Start bpython shell."""
        try:
            from bpython import embed
            embed(local_vars)
        except KeyboardInterrupt:
            print("\nbpython session interrupted")
    
    def python(self, local_vars):
        """Start standard Python shell."""
        Console(locals=local_vars).interact()
    
    def shell(self, dbname: Optional[str]):
        """Setup and start the shell with optional database connection."""
        local_vars = {
            'openerp': odoo,
            'odoo': odoo,
        }
        
        if dbname:
            try:
                threading.current_thread().dbname = dbname
                self._registry = Registry(dbname)
                
                with self._registry.cursor() as cr:
                    self._cursor = cr
                    uid = odoo.SUPERUSER_ID
                    ctx = odoo.api.Environment(cr, uid, {})['res.users'].context_get()
                    env = odoo.api.Environment(cr, uid, ctx)
                    local_vars['env'] = env
                    local_vars['self'] = env.user
                    
                    try:
                        self.console(local_vars)
                    finally:
                        cr.rollback()
                        self._cursor = None
                        
            except KeyboardInterrupt:
                _logger.info("Database shell session interrupted")
            except Exception as e:
                _logger.error("Error setting up database environment: %s", e)
                # Fall back to shell without database
                self.console(local_vars)
        else:
            self.console(local_vars)
    
    def run(self, args):
        """Run the shell command."""
        try:
            self.init(args)
            self.shell(config['db_name'])
            return 0
        except KeyboardInterrupt:
            _logger.info("Shell command interrupted")
            return 130
        except Exception as e:
            _logger.error("Error running shell: %s", e)
            return 1


class EnhancedHelp(Command):
    """Display the list of available commands with enhanced formatting"""
    
    name = "help"
    
    def run(self, args):
        """Display help information."""
        try:
            from .core.help import HelpFormatter
            from .core.registry import commands
            
            HelpFormatter.print_help(commands.list_commands())
            return 0
        except KeyboardInterrupt:
            print("\nHelp interrupted")
            return 130
        except Exception as e:
            _logger.error("Error displaying help: %s", e)
            return 1
