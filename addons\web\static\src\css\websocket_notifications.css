/* WebSocket Notification Styles */

.o_websocket_notification {
    border-left: 4px solid #007bff;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

.o_websocket_notification.o_notification_info {
    border-left-color: #17a2b8;
}

.o_websocket_notification.o_notification_success {
    border-left-color: #28a745;
}

.o_websocket_notification.o_notification_warning {
    border-left-color: #ffc107;
}

.o_websocket_notification.o_notification_error {
    border-left-color: #dc3545;
}

.o_websocket_notification.o_notification_urgent {
    border-left-width: 6px;
    animation: pulse 1s infinite, slideInRight 0.3s ease-out;
}

/* WebSocket connection status indicator */
.o_websocket_status {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 9999;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
}

.o_websocket_status.connected {
    background-color: #28a745;
}

.o_websocket_status.connecting {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

.o_websocket_status.disconnected {
    background-color: #dc3545;
}

.o_websocket_status.error {
    background-color: #6f42c1;
}

/* WebSocket notification counter */
.o_websocket_notification_counter {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 0.5s ease-out;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -10px, 0);
    }
    70% {
        transform: translate3d(0, -5px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* WebSocket notification panel */
.o_websocket_notification_panel {
    position: fixed;
    top: 60px;
    right: 10px;
    width: 350px;
    max-height: 400px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9998;
    overflow: hidden;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.o_websocket_notification_panel.show {
    transform: translateX(0);
}

.o_websocket_notification_panel_header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.o_websocket_notification_panel_close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #6c757d;
}

.o_websocket_notification_panel_close:hover {
    color: #495057;
}

.o_websocket_notification_panel_body {
    max-height: 300px;
    overflow-y: auto;
}

.o_websocket_notification_item {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.o_websocket_notification_item:hover {
    background-color: #f8f9fa;
}

.o_websocket_notification_item:last-child {
    border-bottom: none;
}

.o_websocket_notification_item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.o_websocket_notification_item_title {
    font-weight: bold;
    margin-bottom: 4px;
    color: #212529;
}

.o_websocket_notification_item_message {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

.o_websocket_notification_item_time {
    font-size: 11px;
    color: #adb5bd;
    margin-top: 4px;
}

.o_websocket_notification_item_priority {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 8px;
}

.o_websocket_notification_item_priority.high {
    background-color: #fff3cd;
    color: #856404;
}

.o_websocket_notification_item_priority.urgent {
    background-color: #f8d7da;
    color: #721c24;
}

/* Empty state */
.o_websocket_notification_panel_empty {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.o_websocket_notification_panel_empty_icon {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .o_websocket_notification_panel {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
    }
    
    .o_websocket_status {
        top: 5px;
        right: 5px;
        padding: 3px 8px;
        font-size: 11px;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .o_websocket_notification_panel {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .o_websocket_notification_panel_header {
        background: #1a202c;
        border-bottom-color: #4a5568;
        color: #e2e8f0;
    }
    
    .o_websocket_notification_item {
        border-bottom-color: #4a5568;
    }
    
    .o_websocket_notification_item:hover {
        background-color: #1a202c;
    }
    
    .o_websocket_notification_item.unread {
        background-color: #2a4365;
        border-left-color: #63b3ed;
    }
    
    .o_websocket_notification_item_title {
        color: #e2e8f0;
    }
    
    .o_websocket_notification_item_message {
        color: #a0aec0;
    }
    
    .o_websocket_notification_item_time {
        color: #718096;
    }
}
