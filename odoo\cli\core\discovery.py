# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Command Discovery

This module handles automatic discovery of CLI commands from modules.
"""

import logging
from pathlib import Path

import odoo
from odoo.modules import get_modules, get_module_path, initialize_sys_path

_logger = logging.getLogger(__name__)


class CommandDiscovery:
    """<PERSON>les discovery of CLI commands from addon modules."""
    
    @staticmethod
    def discover_addon_commands():
        """
        Discover and import CLI commands from addon modules.
        
        This method looks for 'cli' directories in addon modules and
        imports them to register their commands.
        """
        _logger.debug("Starting addon command discovery")
        
        # Temporarily disable logging to avoid noise during discovery
        original_level = logging.root.level
        logging.disable(logging.CRITICAL)
        
        try:
            initialize_sys_path()
            discovered_count = 0
            
            for module in get_modules():
                module_path = get_module_path(module)
                if module_path and (Path(module_path) / 'cli').is_dir():
                    try:
                        __import__(f'odoo.addons.{module}')
                        discovered_count += 1
                        _logger.debug(f"Discovered CLI commands in module: {module}")
                    except Import<PERSON>rror as e:
                        _logger.warning(f"Failed to import CLI commands from {module}: {e}")
                    except Exception as e:
                        _logger.error(f"Unexpected error importing CLI commands from {module}: {e}")
            
            _logger.debug(f"Command discovery completed. Found {discovered_count} modules with CLI commands")
            
        finally:
            # Restore original logging level
            logging.disable(original_level)
    
    @staticmethod
    def parse_addons_path_arg(args):
        """
        Parse the --addons-path argument from command line args.
        
        Args:
            args: List of command-line arguments
            
        Returns:
            tuple: (remaining_args, addons_path_found)
        """
        if len(args) > 1 and args[0].startswith('--addons-path=') and not args[1].startswith("-"):
            # Parse only the addons-path, do not setup the logger...
            odoo.tools.config._parse_config([args[0]])
            return args[1:], True
        return args, False
