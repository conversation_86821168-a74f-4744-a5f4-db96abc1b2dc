# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Authentication Service for Odoo

This module provides authentication and authorization services for WebSocket
connections, including session validation, token-based authentication,
and permission checking.
"""

import jwt
import logging
import hashlib
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, Tuple

from starlette.websockets import WebSocket

import odoo
from odoo import api, registry, http
from odoo.exceptions import AccessError, UserError
from odoo.tools import config

_logger = logging.getLogger(__name__)


class WebSocketAuthenticationError(Exception):
    """Custom exception for WebSocket authentication errors"""
    pass


class WebSocketTokenManager:
    """
    Manages WebSocket authentication tokens
    """
    
    def __init__(self):
        self.secret_key = self._get_secret_key()
        self.token_expiry = 3600  # 1 hour
        self.active_tokens = {}  # In-memory token store
    
    def _get_secret_key(self) -> str:
        """Get or generate secret key for JWT tokens"""
        # Use Odoo's secret key if available, otherwise generate one
        secret = config.get('websocket_secret_key')
        if not secret:
            secret = config.get('secret_key', secrets.token_urlsafe(32))
        return secret
    
    def generate_token(self, user_id: int, db_name: str, session_id: str = None) -> str:
        """Generate a JWT token for WebSocket authentication"""
        payload = {
            'user_id': user_id,
            'db_name': db_name,
            'session_id': session_id,
            'iat': datetime.utcnow(),
            'exp': datetime.utcnow() + timedelta(seconds=self.token_expiry),
            'jti': secrets.token_urlsafe(16)  # Unique token ID
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm='HS256')
        
        # Store token for validation
        self.active_tokens[payload['jti']] = {
            'user_id': user_id,
            'db_name': db_name,
            'created_at': datetime.utcnow()
        }
        
        return token
    
    def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate a JWT token and return payload"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # Check if token is still active
            jti = payload.get('jti')
            if jti not in self.active_tokens:
                raise WebSocketAuthenticationError("Token has been revoked")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise WebSocketAuthenticationError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise WebSocketAuthenticationError(f"Invalid token: {e}")
    
    def revoke_token(self, token: str):
        """Revoke a token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'], options={"verify_exp": False})
            jti = payload.get('jti')
            if jti in self.active_tokens:
                del self.active_tokens[jti]
        except:
            pass  # Token was already invalid
    
    def cleanup_expired_tokens(self):
        """Clean up expired tokens from memory"""
        now = datetime.utcnow()
        expired_tokens = []
        
        for jti, token_data in self.active_tokens.items():
            if (now - token_data['created_at']).total_seconds() > self.token_expiry:
                expired_tokens.append(jti)
        
        for jti in expired_tokens:
            del self.active_tokens[jti]


class WebSocketAuthenticator:
    """
    Handles WebSocket authentication and authorization
    """
    
    def __init__(self):
        self.token_manager = WebSocketTokenManager()
        self.rate_limiter = WebSocketRateLimiter()
    
    def authenticate_websocket(self, websocket: WebSocket) -> Tuple[int, str, str]:
        """
        Authenticate WebSocket connection using various methods
        Returns (user_id, db_name, session_id) or raises exception
        """
        # Check rate limiting first
        client_ip = self._get_client_ip(websocket)
        if not self.rate_limiter.check_rate_limit(client_ip):
            raise WebSocketAuthenticationError("Rate limit exceeded")
        
        # Try different authentication methods
        auth_result = None
        
        # Method 1: JWT Token authentication
        try:
            auth_result = self._authenticate_with_token(websocket)
        except WebSocketAuthenticationError:
            pass
        
        # Method 2: Session-based authentication
        if not auth_result:
            try:
                auth_result = self._authenticate_with_session(websocket)
            except WebSocketAuthenticationError:
                pass
        
        # Method 3: API Key authentication (for external integrations)
        if not auth_result:
            try:
                auth_result = self._authenticate_with_api_key(websocket)
            except WebSocketAuthenticationError:
                pass
        
        if not auth_result:
            self.rate_limiter.record_failed_attempt(client_ip)
            raise WebSocketAuthenticationError("Authentication failed")
        
        user_id, db_name, session_id = auth_result
        
        # Additional security checks
        self._validate_user_access(user_id, db_name)
        
        return user_id, db_name, session_id
    
    def _get_client_ip(self, websocket: WebSocket) -> str:
        """Get client IP address from WebSocket"""
        if websocket.client:
            return websocket.client.host
        return "unknown"
    
    def _authenticate_with_token(self, websocket: WebSocket) -> Tuple[int, str, str]:
        """Authenticate using JWT token"""
        query_params = dict(websocket.query_params)
        headers = dict(websocket.headers)
        
        # Get token from query params or Authorization header
        token = query_params.get('token')
        if not token:
            auth_header = headers.get('authorization', '')
            if auth_header.startswith('Bearer '):
                token = auth_header[7:]
        
        if not token:
            raise WebSocketAuthenticationError("No token provided")
        
        payload = self.token_manager.validate_token(token)
        return payload['user_id'], payload['db_name'], payload.get('session_id')
    
    def _authenticate_with_session(self, websocket: WebSocket) -> Tuple[int, str, str]:
        """Authenticate using session ID"""
        query_params = dict(websocket.query_params)
        headers = dict(websocket.headers)
        
        # Get session_id from query params or cookies
        session_id = query_params.get('session_id')
        if not session_id:
            cookie_header = headers.get('cookie', '')
            for cookie in cookie_header.split(';'):
                if 'session_id=' in cookie:
                    session_id = cookie.split('session_id=')[1].split(';')[0].strip()
                    break
        
        if not session_id:
            raise WebSocketAuthenticationError("No session ID provided")
        
        # Get database name
        db_name = query_params.get('db')
        if not db_name:
            raise WebSocketAuthenticationError("No database specified")
        
        # Validate session
        try:
            session_store = http.root.session_store
            session = session_store.get(session_id)
            
            if not session or not hasattr(session, 'uid') or not session.uid:
                raise WebSocketAuthenticationError("Invalid or expired session")
            
            # Verify database access
            if hasattr(session, 'db') and session.db != db_name:
                raise WebSocketAuthenticationError("Database mismatch")
            
            return session.uid, db_name, session_id
            
        except Exception as e:
            raise WebSocketAuthenticationError(f"Session validation failed: {e}")
    
    def _authenticate_with_api_key(self, websocket: WebSocket) -> Tuple[int, str, str]:
        """Authenticate using API key (for external integrations)"""
        query_params = dict(websocket.query_params)
        headers = dict(websocket.headers)
        
        api_key = query_params.get('api_key') or headers.get('x-api-key')
        if not api_key:
            raise WebSocketAuthenticationError("No API key provided")
        
        db_name = query_params.get('db')
        if not db_name:
            raise WebSocketAuthenticationError("No database specified")
        
        # Validate API key against database
        try:
            with registry(db_name).cursor() as cr:
                env = api.Environment(cr, odoo.SUPERUSER_ID, {})
                
                # Look for API key in res.users or custom model
                # This is a simplified implementation - you might want to use a dedicated API key model
                user = env['res.users'].search([('api_key', '=', api_key)], limit=1)
                if not user:
                    raise WebSocketAuthenticationError("Invalid API key")
                
                return user.id, db_name, None
                
        except Exception as e:
            raise WebSocketAuthenticationError(f"API key validation failed: {e}")
    
    def _validate_user_access(self, user_id: int, db_name: str):
        """Validate user has access to WebSocket functionality"""
        try:
            with registry(db_name).cursor() as cr:
                env = api.Environment(cr, user_id, {})
                
                # Check if user is active
                user = env['res.users'].browse(user_id)
                if not user.exists() or not user.active:
                    raise WebSocketAuthenticationError("User is not active")
                
                # Check WebSocket permissions (if you have custom groups)
                # if not user.has_group('base.group_websocket_user'):
                #     raise WebSocketAuthenticationError("User does not have WebSocket access")
                
        except Exception as e:
            raise WebSocketAuthenticationError(f"User validation failed: {e}")
    
    def generate_websocket_token(self, user_id: int, db_name: str, session_id: str = None) -> str:
        """Generate a WebSocket token for a user"""
        return self.token_manager.generate_token(user_id, db_name, session_id)


class WebSocketRateLimiter:
    """
    Rate limiter for WebSocket connections
    """
    
    def __init__(self):
        self.attempts = {}  # IP -> (count, last_attempt)
        self.max_attempts = 10  # Max attempts per window
        self.window_size = 300  # 5 minutes
        self.lockout_duration = 900  # 15 minutes
    
    def check_rate_limit(self, client_ip: str) -> bool:
        """Check if client IP is within rate limits"""
        now = datetime.utcnow()
        
        if client_ip in self.attempts:
            count, last_attempt = self.attempts[client_ip]
            
            # Reset if window has passed
            if (now - last_attempt).total_seconds() > self.window_size:
                self.attempts[client_ip] = (0, now)
                return True
            
            # Check if still in lockout period
            if count >= self.max_attempts:
                if (now - last_attempt).total_seconds() < self.lockout_duration:
                    return False
                else:
                    # Lockout period has passed, reset
                    self.attempts[client_ip] = (0, now)
                    return True
            
            return count < self.max_attempts
        
        # First attempt from this IP
        self.attempts[client_ip] = (0, now)
        return True
    
    def record_failed_attempt(self, client_ip: str):
        """Record a failed authentication attempt"""
        now = datetime.utcnow()
        
        if client_ip in self.attempts:
            count, _ = self.attempts[client_ip]
            self.attempts[client_ip] = (count + 1, now)
        else:
            self.attempts[client_ip] = (1, now)


# Global authenticator instance
websocket_authenticator = WebSocketAuthenticator()


def get_websocket_authenticator():
    """Get the global WebSocket authenticator"""
    return websocket_authenticator
