# Odoo WebSocket Implementation Documentation

## Overview

This document describes the WebSocket implementation for Odoo ERP, providing real-time communication capabilities for enhanced user experience and system responsiveness.

## Features

- **Real-time Notifications**: Instant delivery of system notifications to connected users
- **Multi-user Support**: Handle multiple concurrent WebSocket connections
- **Authentication Integration**: Secure connections using Odoo's authentication system
- **Channel Subscriptions**: Subscribe to specific channels for targeted notifications
- **Auto-reconnection**: Automatic reconnection with exponential backoff
- **Rate Limiting**: Protection against abuse and DoS attacks
- **Compression**: Message compression for improved performance
- **Metrics and Monitoring**: Built-in metrics collection and monitoring

## Architecture

### Server-side Components

1. **ASGI Application** (`odoo/asgi.py`)
   - Main ASGI application with WebSocket route handling
   - Integration with Starlette framework

2. **WebSocket Controller** (`addons/web/controllers/websocket.py`)
   - WebSocket connection management
   - Message handling and routing
   - Authentication and authorization

3. **WebSocket Services** (`odoo/service/websocket_service.py`)
   - Connection manager for tracking active connections
   - Notification service for real-time message delivery
   - Metrics collection and cleanup services

4. **Authentication Service** (`odoo/service/websocket_auth.py`)
   - Multi-method authentication (session, JWT, API key)
   - Rate limiting and security features
   - Token management

5. **Configuration** (`odoo/tools/websocket_config.py`)
   - Centralized configuration management
   - Validation and default settings

### Client-side Components

1. **WebSocket Client** (`addons/web/static/src/js/websocket_client.js`)
   - JavaScript WebSocket client implementation
   - Auto-reconnection and ping/pong handling
   - Integration with Odoo's service registry

2. **Notification Integration**
   - Integration with Odoo's notification system
   - Browser notification support
   - Visual notification styling

## Installation and Setup

### 1. Install Dependencies

The required WebSocket dependencies are already included in `requirements.txt`:

```bash
pip install websockets wsproto
```

### 2. Configuration

Copy the example configuration file and modify as needed:

```bash
cp conf/websocket.conf.example conf/websocket.conf
```

Key configuration options:

```ini
# Enable WebSocket functionality
websocket_enabled = True

# Connection limits
websocket_max_connections = 1000
websocket_max_connections_per_user = 10

# Authentication
websocket_auth_required = True
websocket_session_auth = True
websocket_token_auth = True

# Rate limiting
websocket_rate_limit_enabled = True
websocket_rate_limit_requests = 100
```

### 3. Start Odoo

Start Odoo normally. The WebSocket server will be integrated with the main ASGI server:

```bash
python odoo-bin --config=conf/websocket.conf
```

## Usage

### Server-side Usage

#### Sending Notifications

```python
# Send notification to specific users
self.env['websocket.notification'].create_notification(
    title='New Message',
    message='You have received a new message',
    notification_type='info',
    target_type='user',
    target_users=[user.id]
)

# Send broadcast notification
self.env['websocket.notification'].create_notification(
    title='System Maintenance',
    message='System will be down for maintenance',
    notification_type='warning',
    target_type='all'
)

# Send channel notification
self.env['websocket.notification'].create_notification(
    title='Channel Update',
    message='New update in your subscribed channel',
    notification_type='info',
    target_type='channel',
    channel_name='project_updates'
)
```

#### Using WebSocket Service

```python
from odoo.service.websocket_service import get_websocket_service_manager

# Get service manager
service_manager = get_websocket_service_manager()
notification_service = service_manager.get_notification_service()

# Send real-time notification
await notification_service.send_user_notification(
    user_ids=[1, 2, 3],
    message={
        'type': 'custom_event',
        'data': {'key': 'value'}
    },
    db_name='mydb'
)
```

### Client-side Usage

#### Basic Connection

```javascript
import { websocketClient } from '@web/websocket_client';

// The client auto-connects when the page loads
// Listen for connection events
websocketClient.addEventListener('connected', (event) => {
    console.log('WebSocket connected');
});

websocketClient.addEventListener('disconnected', (event) => {
    console.log('WebSocket disconnected');
});
```

#### Handling Notifications

```javascript
// Listen for notifications
websocketClient.addEventListener('notification', (event) => {
    const notification = event.detail;
    console.log('Received notification:', notification);
    
    // Handle the notification
    if (notification.notification_type === 'urgent') {
        // Show urgent notification
        showUrgentNotification(notification);
    }
});
```

#### Channel Subscriptions

```javascript
// Subscribe to a channel
websocketClient.subscribe('project_updates');

// Unsubscribe from a channel
websocketClient.unsubscribe('project_updates');

// Send custom message
websocketClient.send({
    type: 'custom_message',
    data: { key: 'value' }
});
```

## API Reference

### WebSocket Message Types

#### Client to Server

1. **ping**: Heartbeat message
   ```json
   { "type": "ping", "timestamp": 1234567890 }
   ```

2. **subscribe**: Subscribe to channel
   ```json
   { "type": "subscribe", "channel": "channel_name" }
   ```

3. **unsubscribe**: Unsubscribe from channel
   ```json
   { "type": "unsubscribe", "channel": "channel_name" }
   ```

4. **notification_read**: Mark notification as read
   ```json
   { "type": "notification_read", "notification_id": 123 }
   ```

#### Server to Client

1. **pong**: Heartbeat response
   ```json
   { "type": "pong", "timestamp": 1234567890 }
   ```

2. **welcome**: Connection welcome message
   ```json
   {
     "type": "welcome",
     "user_id": 1,
     "db_name": "mydb",
     "timestamp": "2023-01-01T00:00:00Z"
   }
   ```

3. **notification**: Real-time notification
   ```json
   {
     "type": "notification",
     "id": 123,
     "notification_type": "info",
     "title": "Title",
     "message": "Message",
     "priority": "normal",
     "timestamp": "2023-01-01T00:00:00Z",
     "data": {}
   }
   ```

### Configuration Options

See `conf/websocket.conf.example` for complete configuration reference.

## Security Considerations

1. **Authentication**: All connections require valid authentication
2. **Rate Limiting**: Built-in protection against abuse
3. **CORS**: Configurable CORS settings for cross-origin requests
4. **SSL/TLS**: Support for encrypted connections
5. **Token Expiry**: JWT tokens have configurable expiry times
6. **Connection Limits**: Per-user and per-IP connection limits

## Performance Tuning

### Server Configuration

```ini
# Increase connection limits for high-traffic sites
websocket_max_connections = 5000
websocket_max_connections_per_user = 20

# Enable compression for better bandwidth usage
websocket_compression_enabled = True
websocket_compression_level = 6

# Adjust timeouts for your use case
websocket_idle_timeout = 900  # 15 minutes
websocket_ping_interval = 30  # 30 seconds
```

### Database Optimization

1. **Indexes**: Ensure proper indexes on notification tables
2. **Cleanup**: Enable automatic cleanup of old notifications
3. **Partitioning**: Consider table partitioning for large installations

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if WebSocket is enabled in configuration
   - Verify firewall settings
   - Check server logs for errors

2. **Authentication Failures**
   - Verify session is valid
   - Check database permissions
   - Review authentication logs

3. **High Memory Usage**
   - Reduce connection limits
   - Enable cleanup services
   - Monitor connection metrics

### Debugging

Enable debug logging:

```ini
websocket_debug = True
websocket_log_connections = True
websocket_log_messages = True
```

Check logs:

```bash
tail -f /var/log/odoo/odoo.log | grep -i websocket
```

### Monitoring

Access WebSocket metrics through the admin interface:
- Go to Settings > Technical > WebSocket Notifications
- View connection statistics and notification history
- Monitor performance metrics

## Best Practices

1. **Connection Management**
   - Implement proper connection cleanup
   - Use appropriate timeout values
   - Monitor connection counts

2. **Message Design**
   - Keep messages small and focused
   - Use compression for large messages
   - Implement proper error handling

3. **Security**
   - Always require authentication
   - Use rate limiting
   - Implement proper CORS policies
   - Use SSL/TLS in production

4. **Performance**
   - Monitor memory usage
   - Use connection pooling
   - Implement proper caching strategies

## Migration Guide

### From Previous Versions

If upgrading from a version without WebSocket support:

1. Install new dependencies
2. Update configuration files
3. Restart Odoo server
4. Test WebSocket connectivity
5. Update custom modules if needed

## Testing

### Manual Testing

1. **Connection Test**
   ```javascript
   // Open browser console and test connection
   const ws = new WebSocket('ws://localhost:8069/websocket?session_id=YOUR_SESSION&db=YOUR_DB');
   ws.onopen = () => console.log('Connected');
   ws.onmessage = (event) => console.log('Message:', event.data);
   ```

2. **Notification Test**
   ```python
   # In Odoo shell
   self.env['websocket.notification'].create_notification(
       title='Test Notification',
       message='This is a test message',
       target_type='user',
       target_users=[self.env.user.id]
   )
   ```

### Automated Testing

See `addons/web/tests/test_websocket.py` for automated test examples.

## Support and Contributing

For issues and contributions:
1. Check existing documentation
2. Review configuration settings
3. Check server logs
4. Submit detailed bug reports
5. Follow coding standards for contributions
