# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
CLI Exception Handler

This module provides centralized exception handling for the Odoo CLI.
"""

import logging
import sys
import traceback
from typing import Optional, Callable, Any
from contextlib import contextmanager

from .exceptions import (
    C<PERSON>IError, 
    CommandNotFoundError, 
    KeyboardInterruptError,
    ConfigurationError,
    ValidationError
)

_logger = logging.getLogger(__name__)


class CLIExceptionHandler:
    """Centralized exception handler for CLI operations."""
    
    def __init__(self, debug: bool = False, verbose: bool = False):
        self.debug = debug
        self.verbose = verbose
        self._error_handlers = {
            KeyboardInterrupt: self._handle_keyboard_interrupt,
            KeyboardInterruptError: self._handle_keyboard_interrupt_error,
            CommandNotFoundError: self._handle_command_not_found,
            ConfigurationError: self._handle_configuration_error,
            ValidationError: self._handle_validation_error,
            CLIError: self._handle_cli_error,
        }
    
    def handle_exception(self, exc: Exception) -> int:
        """
        Handle an exception and return appropriate exit code.
        
        Args:
            exc: Exception to handle
            
        Returns:
            Exit code
        """
        exc_type = type(exc)
        
        # Find the most specific handler
        handler = None
        for error_type, error_handler in self._error_handlers.items():
            if isinstance(exc, error_type):
                handler = error_handler
                break
        
        if handler:
            return handler(exc)
        else:
            return self._handle_unexpected_error(exc)
    
    def _handle_keyboard_interrupt(self, exc: KeyboardInterrupt) -> int:
        """Handle KeyboardInterrupt exceptions."""
        if self.verbose:
            print("\nOperation interrupted by user", file=sys.stderr)
        else:
            print("\nInterrupted", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return 130  # Standard exit code for SIGINT
    
    def _handle_keyboard_interrupt_error(self, exc: KeyboardInterruptError) -> int:
        """Handle KeyboardInterruptError exceptions."""
        if self.verbose:
            print(f"\n{exc.message}", file=sys.stderr)
        else:
            print("\nInterrupted", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return exc.exit_code
    
    def _handle_command_not_found(self, exc: CommandNotFoundError) -> int:
        """Handle CommandNotFoundError exceptions."""
        print(f"Error: {exc.message}", file=sys.stderr)
        
        if exc.available_commands and self.verbose:
            print("\nDid you mean one of these?", file=sys.stderr)
            for cmd in sorted(exc.available_commands)[:5]:  # Show max 5 suggestions
                print(f"  {cmd}", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return exc.exit_code
    
    def _handle_configuration_error(self, exc: ConfigurationError) -> int:
        """Handle ConfigurationError exceptions."""
        print(f"Configuration Error: {exc.message}", file=sys.stderr)
        
        if self.verbose:
            print("\nPlease check your configuration file and command line arguments.", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return exc.exit_code
    
    def _handle_validation_error(self, exc: ValidationError) -> int:
        """Handle ValidationError exceptions."""
        print(f"Validation Error: {exc.message}", file=sys.stderr)
        
        if self.verbose:
            print("\nPlease check your command arguments and try again.", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return exc.exit_code
    
    def _handle_cli_error(self, exc: CLIError) -> int:
        """Handle generic CLIError exceptions."""
        print(f"Error: {exc.message}", file=sys.stderr)
        
        if self.debug:
            traceback.print_exc()
        
        return exc.exit_code
    
    def _handle_unexpected_error(self, exc: Exception) -> int:
        """Handle unexpected exceptions."""
        if self.verbose:
            print(f"Unexpected error: {exc}", file=sys.stderr)
        else:
            print("An unexpected error occurred", file=sys.stderr)
        
        if self.debug or self.verbose:
            traceback.print_exc()
        else:
            print("Use --debug for more information", file=sys.stderr)
        
        return 1
    
    def register_handler(self, exc_type: type, handler: Callable[[Exception], int]) -> None:
        """
        Register a custom exception handler.
        
        Args:
            exc_type: Exception type to handle
            handler: Handler function that takes exception and returns exit code
        """
        self._error_handlers[exc_type] = handler


@contextmanager
def exception_context(debug: bool = False, verbose: bool = False):
    """
    Context manager for handling exceptions in CLI operations.
    
    Args:
        debug: Enable debug mode
        verbose: Enable verbose mode
        
    Usage:
        with exception_context(debug=True) as handler:
            # Your CLI code here
            pass
    """
    handler = CLIExceptionHandler(debug=debug, verbose=verbose)
    
    try:
        yield handler
    except Exception as exc:
        exit_code = handler.handle_exception(exc)
        sys.exit(exit_code)


def handle_cli_exception(exc: Exception, debug: bool = False, verbose: bool = False) -> None:
    """
    Handle a CLI exception and exit with appropriate code.
    
    Args:
        exc: Exception to handle
        debug: Enable debug mode
        verbose: Enable verbose mode
    """
    handler = CLIExceptionHandler(debug=debug, verbose=verbose)
    exit_code = handler.handle_exception(exc)
    sys.exit(exit_code)


def setup_exception_handling(debug: bool = False, verbose: bool = False) -> CLIExceptionHandler:
    """
    Setup global exception handling for CLI.
    
    Args:
        debug: Enable debug mode
        verbose: Enable verbose mode
        
    Returns:
        Exception handler instance
    """
    handler = CLIExceptionHandler(debug=debug, verbose=verbose)
    
    def excepthook(exc_type, exc_value, exc_traceback):
        """Custom exception hook for unhandled exceptions."""
        if issubclass(exc_type, KeyboardInterrupt):
            # Handle KeyboardInterrupt specially
            handler._handle_keyboard_interrupt(exc_value)
            sys.exit(130)
        else:
            # Handle other exceptions
            exit_code = handler.handle_exception(exc_value)
            sys.exit(exit_code)
    
    # Install the custom exception hook
    sys.excepthook = excepthook
    
    return handler
