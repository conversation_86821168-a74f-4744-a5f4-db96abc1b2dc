# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Help Formatter

This module provides help formatting functionality for the Odoo CLI.
"""

import sys
from pathlib import Path
from typing import Dict, Type

from .registry import Command


class HelpFormatter:
    """Formats help text for CLI commands."""
    
    HELP_TEMPLATE = """\
Odoo CLI, use '{odoo_bin} --help' for regular server options.

Available commands:
{command_list}

Use '{odoo_bin} <command> --help' for individual command help.

Global options:
  --addons-path=PATH    Specify additional addons paths
  --help               Show this help message
  --version            Show version information

For more information, visit: https://www.odoo.com/documentation/"""
    
    @classmethod
    def format_command_list(cls, commands: Dict[str, Type[Command]]) -> str:
        """
        Format the list of available commands.
        
        Args:
            commands: Dictionary of command name to command class
            
        Returns:
            Formatted command list string
        """
        if not commands:
            return "    No commands available"
        
        # Calculate padding for alignment
        max_name_length = max(len(name) for name in commands.keys())
        padding = max_name_length + 4
        
        command_lines = []
        for name, command_class in sorted(commands.items()):
            description = command_class.__doc__ or ""
            description = description.strip().split('\n')[0]  # First line only
            command_lines.append(f"    {name.ljust(padding)}{description}")
        
        return "\n".join(command_lines)
    
    @classmethod
    def format_help(cls, commands: Dict[str, Type[Command]]) -> str:
        """
        Format the main help text.
        
        Args:
            commands: Dictionary of command name to command class
            
        Returns:
            Formatted help text
        """
        odoo_bin = Path(sys.argv[0]).name
        command_list = cls.format_command_list(commands)
        
        return cls.HELP_TEMPLATE.format(
            odoo_bin=odoo_bin,
            command_list=command_list
        )
    
    @classmethod
    def print_help(cls, commands: Dict[str, Type[Command]]) -> None:
        """
        Print the help text to stdout.
        
        Args:
            commands: Dictionary of command name to command class
        """
        print(cls.format_help(commands))


class HelpCommand(Command):
    """Display the list of available commands"""
    
    name = "help"
    
    def run(self, args):
        """Display help information."""
        from .registry import commands
        HelpFormatter.print_help(commands.list_commands())
