# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Refactored Odoo Server Command

This module provides a refactored version of the server command with
better separation of concerns and modular structure.
"""

import atexit
import csv
import logging
import os
import sys
from pathlib import Path
from typing import List, Optional

from psycopg2.errors import InsufficientPrivilege

import odoo
from .core import Command
from .core.utils import CLIUtils, ConfigurationManager
from .core.exceptions import ConfigurationError

__author__ = odoo.release.author
__version__ = odoo.release.version

_logger = logging.getLogger('odoo')


class ServerConfiguration:
    """Handles server configuration and validation."""
    
    def __init__(self):
        self.config = None
    
    def parse_and_validate(self, args: List[str]) -> None:
        """Parse and validate server configuration."""
        # Parse configuration
        ConfigurationManager.parse_config(args, setup_logging=True)
        self.config = odoo.tools.config
        
        # Perform validation checks
        CLIUtils.check_root_user()
        CLIUtils.check_postgres_user()
        CLIUtils.check_python_version()
        
        # Report configuration
        CLIUtils.report_configuration()
        
        # Setup CSV limits
        CLIUtils.setup_csv_limits()


class DatabaseManager:
    """Handles database operations for the server."""
    
    def __init__(self, config):
        self.config = config
    
    def prepare_databases(self) -> List[str]:
        """
        Prepare databases for preloading.
        
        Returns:
            List of database names to preload
        """
        preload = []
        if self.config['db_name']:
            preload = self.config['db_name'].split(',')
            for db_name in preload:
                self._create_database_if_needed(db_name)
        return preload
    
    def _create_database_if_needed(self, db_name: str) -> None:
        """Create database if it doesn't exist."""
        try:
            odoo.service.db._create_empty_database(db_name)
            self.config['init']['base'] = True
        except InsufficientPrivilege as err:
            # We use an INFO loglevel on purpose in order to avoid
            # reporting unnecessary warnings on build environment
            # using restricted database access.
            _logger.info(
                "Could not determine if database %s exists, "
                "skipping auto-creation: %s", db_name, err
            )
        except odoo.service.db.DatabaseExists:
            pass


class TranslationManager:
    """Handles translation import/export operations."""
    
    def __init__(self, config):
        self.config = config
    
    def handle_translation_export(self) -> bool:
        """
        Handle translation export if requested.
        
        Returns:
            True if translation was exported (should exit), False otherwise
        """
        if not self.config["translate_out"]:
            return False
        
        self._export_translation()
        return True
    
    def handle_translation_import(self) -> bool:
        """
        Handle translation import if requested.
        
        Returns:
            True if translation was imported (should exit), False otherwise
        """
        if not self.config["translate_in"]:
            return False
        
        self._import_translation()
        return True
    
    def _export_translation(self) -> None:
        """Export translations to file."""
        dbname = self.config['db_name']
        
        if self.config["language"]:
            msg = f"language {self.config['language']}"
        else:
            msg = "new language"
        
        _logger.info('writing translation file for %s to %s', 
                    msg, self.config["translate_out"])
        
        fileformat = os.path.splitext(self.config["translate_out"])[-1][1:].lower()
        # .pot is the same fileformat as .po
        if fileformat == "pot":
            fileformat = "po"
        
        with open(self.config["translate_out"], "wb") as buf:
            registry = odoo.modules.registry.Registry.new(dbname)
            with registry.cursor() as cr:
                odoo.tools.translate.trans_export(
                    self.config["language"],
                    self.config["translate_modules"] or ["all"], 
                    buf, fileformat, cr
                )
        
        _logger.info('translation file written successfully')
    
    def _import_translation(self) -> None:
        """Import translations from file."""
        overwrite = self.config["overwrite_existing_translations"]
        dbname = self.config['db_name']
        
        registry = odoo.modules.registry.Registry.new(dbname)
        with registry.cursor() as cr:
            translation_importer = odoo.tools.translate.TranslationImporter(cr)
            translation_importer.load_file(
                self.config["translate_in"], 
                self.config["language"]
            )
            translation_importer.save(overwrite=overwrite)


class PidFileManager:
    """Handles PID file operations."""
    
    def __init__(self, config):
        self.config = config
    
    def setup(self) -> None:
        """Create a file with the process id written in it."""
        if not odoo.evented and self.config['pidfile']:
            pid = os.getpid()
            with open(self.config['pidfile'], 'w') as fd:
                fd.write(str(pid))
            atexit.register(self._remove_pid_file, pid)
    
    def _remove_pid_file(self, main_pid: int) -> None:
        """Remove PID file if this is the main process."""
        if self.config['pidfile'] and main_pid == os.getpid():
            try:
                os.unlink(self.config['pidfile'])
            except OSError:
                pass


class ServerRunner:
    """Handles the actual server execution."""
    
    def __init__(self):
        self.config_manager = ServerConfiguration()
        self.db_manager = None
        self.translation_manager = None
        self.pid_manager = None
    
    def initialize(self, args: List[str]) -> None:
        """Initialize all server components."""
        # Parse and validate configuration
        self.config_manager.parse_and_validate(args)
        config = self.config_manager.config
        
        # Initialize managers
        self.db_manager = DatabaseManager(config)
        self.translation_manager = TranslationManager(config)
        self.pid_manager = PidFileManager(config)
    
    def run(self, args: List[str]) -> int:
        """
        Run the server with the given arguments.
        
        Args:
            args: Command line arguments
            
        Returns:
            Exit code
        """
        # Initialize components
        self.initialize(args)
        config = self.config_manager.config
        
        # Handle translation operations (these exit immediately)
        if self.translation_manager.handle_translation_export():
            return 0
        
        if self.translation_manager.handle_translation_import():
            return 0
        
        # Prepare databases
        preload = self.db_manager.prepare_databases()
        
        # Setup PID file
        self.pid_manager.setup()
        
        # Start the server
        stop = config["stop_after_init"]
        rc = odoo.service.server.start(preload=preload, stop=stop)
        
        return rc if rc else 0


class Server(Command):
    """Start the odoo server (default command)"""
    
    def run(self, args):
        """Run the server command."""
        # Set program name for help text
        odoo.tools.config.parser.prog = self._get_prog_name()
        
        # Create and run server
        runner = ServerRunner()
        return runner.run(args)
