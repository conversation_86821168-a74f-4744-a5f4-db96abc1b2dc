# Odoo WebSocket Configuration Example
# Copy this file to your Odoo configuration directory and modify as needed

[options]
# ============================================================================
# WebSocket Server Configuration
# ============================================================================

# Enable or disable WebSocket functionality
websocket_enabled = True

# WebSocket server host (default: same as HTTP server)
websocket_host = 0.0.0.0

# WebSocket server port (default: same as HTTP server)
# websocket_port = 8069

# WebSocket endpoint path
websocket_path = /websocket

# ============================================================================
# Connection Limits
# ============================================================================

# Maximum total WebSocket connections
websocket_max_connections = 1000

# Maximum connections per user
websocket_max_connections_per_user = 10

# Maximum connections per IP address
websocket_max_connections_per_ip = 50

# ============================================================================
# Message Configuration
# ============================================================================

# Maximum message size in bytes (1MB default)
websocket_max_message_size = 1048576

# Maximum messages per second per connection
websocket_max_messages_per_second = 100

# ============================================================================
# Timeout Settings
# ============================================================================

# Connection timeout in seconds
websocket_connection_timeout = 60

# Idle timeout in seconds (30 minutes default)
websocket_idle_timeout = 1800

# Ping interval in seconds
websocket_ping_interval = 30

# Ping timeout in seconds
websocket_ping_timeout = 10

# ============================================================================
# Authentication Settings
# ============================================================================

# Require authentication for WebSocket connections
websocket_auth_required = True

# Enable session-based authentication
websocket_session_auth = True

# Enable JWT token authentication
websocket_token_auth = True

# Enable API key authentication (for external integrations)
websocket_api_key_auth = False

# JWT token expiry in seconds (1 hour default)
websocket_token_expiry = 3600

# Secret key for JWT tokens (leave empty to use Odoo's secret key)
# websocket_secret_key = your-secret-key-here

# ============================================================================
# Rate Limiting
# ============================================================================

# Enable rate limiting
websocket_rate_limit_enabled = True

# Maximum requests per window
websocket_rate_limit_requests = 100

# Rate limit window in seconds
websocket_rate_limit_window = 60

# Lockout duration in seconds after exceeding limits
websocket_rate_limit_lockout = 300

# ============================================================================
# Compression Settings
# ============================================================================

# Enable message compression
websocket_compression_enabled = True

# Compression level (1-9, higher = better compression but more CPU)
websocket_compression_level = 6

# Minimum message size to compress (bytes)
websocket_compression_threshold = 1024

# ============================================================================
# Logging and Monitoring
# ============================================================================

# WebSocket log level (DEBUG, INFO, WARNING, ERROR)
websocket_log_level = INFO

# Log connection events
websocket_log_connections = True

# Log all messages (WARNING: can generate large logs)
websocket_log_messages = False

# Enable metrics collection
websocket_metrics_enabled = True

# ============================================================================
# Notification Settings
# ============================================================================

# Enable real-time notifications
websocket_notifications_enabled = True

# Notification queue size
websocket_notification_queue_size = 10000

# Notification batch size for processing
websocket_notification_batch_size = 100

# Number of retry attempts for failed notifications
websocket_notification_retry_attempts = 3

# ============================================================================
# Cleanup Settings
# ============================================================================

# Enable automatic cleanup of stale connections
websocket_cleanup_enabled = True

# Cleanup interval in seconds
websocket_cleanup_interval = 300

# Maximum idle time before cleanup (seconds)
websocket_cleanup_max_idle = 1800

# ============================================================================
# Performance Settings
# ============================================================================

# Number of worker threads for WebSocket processing
websocket_worker_threads = 4

# Buffer size for WebSocket connections (bytes)
websocket_buffer_size = 65536

# Connection backlog size
websocket_backlog = 100

# ============================================================================
# Security Settings
# ============================================================================

# Enable CORS for WebSocket connections
websocket_cors_enabled = True

# Allowed CORS origins (* for all, comma-separated for specific)
websocket_cors_origins = *

# Enable SSL/TLS for WebSocket connections
websocket_ssl_enabled = False

# SSL certificate file path
# websocket_ssl_cert = /path/to/cert.pem

# SSL private key file path
# websocket_ssl_key = /path/to/key.pem

# ============================================================================
# Development Settings
# ============================================================================

# Enable debug mode (more verbose logging)
websocket_debug = False

# Enable auto-reload on code changes (development only)
websocket_auto_reload = False

# ============================================================================
# Example Production Configuration
# ============================================================================

# For production environments, consider these settings:
# websocket_max_connections = 5000
# websocket_max_connections_per_user = 5
# websocket_idle_timeout = 900
# websocket_log_messages = False
# websocket_compression_enabled = True
# websocket_ssl_enabled = True
# websocket_debug = False

# ============================================================================
# Example Development Configuration
# ============================================================================

# For development environments, consider these settings:
# websocket_max_connections = 100
# websocket_log_connections = True
# websocket_log_messages = True
# websocket_debug = True
# websocket_auto_reload = True
