#!/usr/bin/env python3
"""
CLI Migration Script

This script helps migrate from the old monolithic CLI system to the new
modular CLI system with enhanced keyboard interrupt handling.
"""

import os
import sys
import shutil
from pathlib import Path
from typing import List, Optional

def backup_original_files():
    """Backup original CLI files before migration."""
    print("Creating backup of original CLI files...")
    
    cli_dir = Path(__file__).parent
    backup_dir = cli_dir / "backup_original"
    
    if backup_dir.exists():
        print(f"Backup directory already exists: {backup_dir}")
        return backup_dir
    
    backup_dir.mkdir()
    
    # Files to backup
    files_to_backup = [
        "command.py",
        "__init__.py",
        "server.py",
    ]
    
    for filename in files_to_backup:
        src = cli_dir / filename
        if src.exists():
            dst = backup_dir / filename
            shutil.copy2(src, dst)
            print(f"Backed up: {filename}")
    
    print(f"Backup completed in: {backup_dir}")
    return backup_dir


def update_command_py():
    """Update command.py to use the new system."""
    print("Updating command.py...")
    
    cli_dir = Path(__file__).parent
    command_file = cli_dir / "command.py"
    command_new_file = cli_dir / "command_new.py"
    
    if command_new_file.exists():
        # Replace old command.py with new version
        if command_file.exists():
            command_file.rename(command_file.with_suffix('.py.old'))
        
        shutil.copy2(command_new_file, command_file)
        print("✓ Updated command.py with new modular system")
    else:
        print("✗ command_new.py not found, skipping update")


def update_init_py():
    """Update __init__.py to use the new system."""
    print("Updating __init__.py...")
    
    cli_dir = Path(__file__).parent
    init_file = cli_dir / "__init__.py"
    init_new_file = cli_dir / "__init___new.py"
    
    if init_new_file.exists():
        # Replace old __init__.py with new version
        if init_file.exists():
            init_file.rename(init_file.with_suffix('.py.old'))
        
        shutil.copy2(init_new_file, init_file)
        print("✓ Updated __init__.py with new modular system")
    else:
        print("✗ __init___new.py not found, skipping update")


def update_server_py():
    """Update server.py to use the refactored version."""
    print("Updating server.py...")
    
    cli_dir = Path(__file__).parent
    server_file = cli_dir / "server.py"
    server_refactored_file = cli_dir / "server_refactored.py"
    
    if server_refactored_file.exists():
        # Replace old server.py with refactored version
        if server_file.exists():
            server_file.rename(server_file.with_suffix('.py.old'))
        
        shutil.copy2(server_refactored_file, server_file)
        print("✓ Updated server.py with refactored modular version")
    else:
        print("✗ server_refactored.py not found, skipping update")


def verify_new_system():
    """Verify that the new system is working correctly."""
    print("Verifying new CLI system...")
    
    try:
        # Test imports
        from .core import Command, CommandRegistry
        from .core.signals import SignalHandler
        from .core.exceptions import CLIError
        print("✓ Core modules import successfully")
        
        # Test command registry
        registry = CommandRegistry()
        
        class TestCommand(Command):
            name = "test_migration"
            def run(self, args):
                return 0
        
        registry.register(TestCommand)
        assert registry.has_command("test_migration")
        print("✓ Command registry works correctly")
        
        # Test signal handler
        handler = SignalHandler()
        handler.register_cleanup(lambda: None)
        print("✓ Signal handling works correctly")
        
        print("✓ New CLI system verification completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Verification failed: {e}")
        return False


def rollback_changes(backup_dir: Path):
    """Rollback changes if migration fails."""
    print("Rolling back changes...")
    
    cli_dir = Path(__file__).parent
    
    # Files to restore
    files_to_restore = [
        "command.py",
        "__init__.py", 
        "server.py",
    ]
    
    for filename in files_to_restore:
        backup_file = backup_dir / filename
        target_file = cli_dir / filename
        
        if backup_file.exists():
            if target_file.exists():
                target_file.unlink()
            shutil.copy2(backup_file, target_file)
            print(f"Restored: {filename}")
    
    print("Rollback completed")


def main():
    """Main migration function."""
    print("=" * 60)
    print("Odoo CLI Migration to Modular System")
    print("=" * 60)
    
    try:
        # Step 1: Backup original files
        backup_dir = backup_original_files()
        
        # Step 2: Update files
        update_command_py()
        update_init_py()
        update_server_py()
        
        # Step 3: Verify new system
        if verify_new_system():
            print("\n" + "=" * 60)
            print("✓ Migration completed successfully!")
            print("=" * 60)
            print("\nTo use the new CLI system:")
            print("1. Set environment variable: export ODOO_USE_NEW_CLI=1")
            print("2. Run: odoo-bin help")
            print("\nTo rollback if needed:")
            print(f"python {__file__} --rollback")
            print("\nBackup files are stored in:")
            print(f"{backup_dir}")
            return 0
        else:
            print("\n" + "=" * 60)
            print("✗ Migration verification failed!")
            print("=" * 60)
            
            response = input("Do you want to rollback changes? (y/N): ")
            if response.lower() in ['y', 'yes']:
                rollback_changes(backup_dir)
            
            return 1
            
    except Exception as e:
        print(f"\n✗ Migration failed with error: {e}")
        print("Please check the error and try again.")
        return 1


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        # Rollback mode
        cli_dir = Path(__file__).parent
        backup_dir = cli_dir / "backup_original"
        
        if backup_dir.exists():
            rollback_changes(backup_dir)
            print("Rollback completed")
        else:
            print("No backup directory found")
    else:
        # Migration mode
        sys.exit(main())
