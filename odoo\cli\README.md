# Odoo CLI Refactoring

This document describes the refactored Odoo CLI system with enhanced modularity, keyboard interrupt handling, and better error management.

## Overview

The Odoo CLI has been refactored to address the following issues:
- **Monolithic structure**: The original `command.py` contained multiple responsibilities
- **Limited signal handling**: Inconsistent keyboard interrupt handling across commands
- **Poor error management**: Limited exception handling and user feedback
- **Tight coupling**: Commands were tightly coupled to the main CLI logic

## New Architecture

### Core Modules

The new CLI system is organized into modular components under `odoo/cli/core/`:

#### `registry.py`
- **CommandRegistry**: Manages command registration and discovery
- **Command**: Enhanced base class with built-in signal handling and exception management

#### `discovery.py`
- **CommandDiscovery**: Handles automatic discovery of CLI commands from addon modules
- Provides clean separation of command discovery logic

#### `signals.py`
- **SignalHandler**: Centralized signal handling for graceful shutdown
- Supports SIGINT, SIGTERM, and SIGHUP on POSIX systems
- Windows console control handler support
- Cleanup function registration and execution

#### `exceptions.py`
- **CLIError**: Base exception for CLI-related errors
- **CommandNotFoundError**: Specific exception for unknown commands
- **KeyboardInterruptError**: Handles keyboard interrupts gracefully
- **ConfigurationError**: Configuration-related errors
- **ValidationError**: Argument validation errors

#### `exception_handler.py`
- **CLIExceptionHandler**: Centralized exception handling with user-friendly messages
- Debug and verbose mode support
- Proper exit code management

#### `utils.py`
- **CLIUtils**: Common utilities for logging, validation, and configuration
- **ConfigurationManager**: Configuration parsing and management
- Extracted common functionality from monolithic files

#### `help.py`
- **HelpFormatter**: Enhanced help text formatting
- **HelpCommand**: Improved help command with better formatting

#### `main.py`
- **CLIMain**: Main CLI application class with proper lifecycle management
- Enhanced command routing and execution

## Key Features

### 1. Enhanced Keyboard Interrupt Handling

All commands now support graceful keyboard interrupt handling:

```python
from odoo.cli.core import Command
from odoo.cli.core.signals import register_cleanup_function

class MyCommand(Command):
    def run(self, args):
        # Register cleanup function
        register_cleanup_function(self.cleanup_resources)
        
        try:
            # Your command logic here
            pass
        except KeyboardInterrupt:
            # Graceful handling of interrupts
            self.logger.info("Command interrupted by user")
            return 130
    
    def cleanup_resources(self):
        # Cleanup logic called on interrupt
        pass
```

### 2. Improved Error Handling

The new system provides consistent error handling across all commands:

```python
from odoo.cli.core.exceptions import ValidationError, ConfigurationError

class MyCommand(Command):
    def run(self, args):
        try:
            self.validate_args(args)
            self.execute_logic(args)
        except ValidationError as e:
            # User-friendly validation error
            raise
        except Exception as e:
            # Unexpected error with proper logging
            self.logger.error("Unexpected error: %s", e)
            raise
```

### 3. Modular Command Structure

Commands are now more modular and easier to maintain:

```python
from odoo.cli.core import Command

class EnhancedCommand(Command):
    """Enhanced command with proper structure."""
    
    name = "mycommand"
    
    def __init__(self):
        super().__init__()
        self.setup_dependencies()
    
    def setup_dependencies(self):
        """Setup command dependencies."""
        pass
    
    def validate_args(self, args):
        """Validate command arguments."""
        pass
    
    def execute_logic(self, args):
        """Execute main command logic."""
        pass
    
    def run(self, args):
        """Main entry point."""
        self.validate_args(args)
        return self.execute_logic(args)
```

## Migration Guide

### For Command Developers

1. **Update imports**:
   ```python
   # Old
   from odoo.cli import Command
   
   # New
   from odoo.cli.core import Command
   ```

2. **Add signal handling**:
   ```python
   def run(self, args):
       # Setup signal handlers (automatic in new base class)
       self.setup_signal_handlers()
       
       # Register cleanup if needed
       self.register_cleanup(self.my_cleanup_function)
   ```

3. **Use enhanced exception handling**:
   ```python
   from odoo.cli.core.exceptions import ValidationError
   
   def run(self, args):
       if not self.validate_input(args):
           raise ValidationError("Invalid arguments provided")
   ```

### For System Integrators

The new CLI system is backward compatible. To enable the new system:

```bash
export ODOO_USE_NEW_CLI=1
odoo-bin help
```

## Backward Compatibility

The refactored system maintains full backward compatibility:
- All existing commands continue to work
- Legacy command imports are preserved
- The old CLI system remains available as fallback

## Testing

The new CLI system includes comprehensive testing:

```python
# Test command registration
from odoo.cli.core.registry import CommandRegistry

registry = CommandRegistry()
registry.register(MyCommand)
assert registry.has_command("mycommand")

# Test signal handling
from odoo.cli.core.signals import SignalHandler

handler = SignalHandler()
handler.register_cleanup(cleanup_function)
handler.setup_handlers()
```

## Benefits

1. **Modularity**: Clear separation of concerns
2. **Maintainability**: Easier to modify and extend
3. **Reliability**: Better error handling and recovery
4. **User Experience**: Graceful interrupt handling and better error messages
5. **Testability**: Modular components are easier to test
6. **Extensibility**: New commands can leverage enhanced base functionality

## Future Enhancements

- Plugin system for third-party commands
- Configuration validation framework
- Enhanced logging and debugging capabilities
- Command completion support
- Interactive command mode
