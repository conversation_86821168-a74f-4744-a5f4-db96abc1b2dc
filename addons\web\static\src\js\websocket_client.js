/** @odoo-module **/

import { registry } from "@web/core/registry";
import { browser } from "@web/core/browser/browser";
import { session } from "@web/session";

/**
 * WebSocket Client for Odoo
 * 
 * Provides real-time communication capabilities for the Odoo web client
 */
class OdooWebSocketClient extends EventTarget {
    constructor() {
        super();
        this.websocket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
        this.pingInterval = null;
        this.pingTimeout = null;
        this.subscriptions = new Set();
        this.messageQueue = [];
        this.config = {
            autoReconnect: true,
            pingInterval: 30000, // 30 seconds
            pingTimeout: 10000,  // 10 seconds
            maxMessageSize: 1024 * 1024, // 1MB
        };
        
        // Bind methods
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
        this.ping = this.ping.bind(this);
    }
    
    /**
     * Connect to the WebSocket server
     */
    connect() {
        if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {
            return;
        }
        
        if (this.isConnected) {
            console.warn('WebSocket is already connected');
            return;
        }
        
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const sessionId = session.session_id;
            const dbName = session.db;
            
            const url = `${protocol}//${host}/websocket?session_id=${sessionId}&db=${dbName}`;
            
            console.log('Connecting to WebSocket:', url);
            
            this.websocket = new WebSocket(url);
            this.websocket.addEventListener('open', this.onOpen);
            this.websocket.addEventListener('message', this.onMessage);
            this.websocket.addEventListener('close', this.onClose);
            this.websocket.addEventListener('error', this.onError);
            
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * Disconnect from the WebSocket server
     */
    disconnect() {
        this.config.autoReconnect = false;
        this.clearPingInterval();
        
        if (this.websocket) {
            this.websocket.removeEventListener('open', this.onOpen);
            this.websocket.removeEventListener('message', this.onMessage);
            this.websocket.removeEventListener('close', this.onClose);
            this.websocket.removeEventListener('error', this.onError);
            
            if (this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.close(1000, 'Client disconnect');
            }
            
            this.websocket = null;
        }
        
        this.isConnected = false;
        this.reconnectAttempts = 0;
    }
    
    /**
     * Send a message to the server
     */
    send(message) {
        if (!this.isConnected) {
            console.warn('WebSocket not connected, queueing message');
            this.messageQueue.push(message);
            return false;
        }
        
        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            
            if (messageStr.length > this.config.maxMessageSize) {
                console.error('Message too large:', messageStr.length);
                return false;
            }
            
            this.websocket.send(messageStr);
            return true;
        } catch (error) {
            console.error('Failed to send WebSocket message:', error);
            return false;
        }
    }
    
    /**
     * Subscribe to a channel
     */
    subscribe(channel) {
        this.subscriptions.add(channel);
        
        if (this.isConnected) {
            this.send({
                type: 'subscribe',
                channel: channel
            });
        }
    }
    
    /**
     * Unsubscribe from a channel
     */
    unsubscribe(channel) {
        this.subscriptions.delete(channel);
        
        if (this.isConnected) {
            this.send({
                type: 'unsubscribe',
                channel: channel
            });
        }
    }
    
    /**
     * Mark a notification as read
     */
    markNotificationRead(notificationId) {
        this.send({
            type: 'notification_read',
            notification_id: notificationId
        });
    }
    
    /**
     * Handle WebSocket open event
     */
    onOpen(event) {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        
        // Start ping interval
        this.startPingInterval();
        
        // Send queued messages
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
        
        // Re-subscribe to channels
        for (const channel of this.subscriptions) {
            this.send({
                type: 'subscribe',
                channel: channel
            });
        }
        
        // Dispatch connected event
        this.dispatchEvent(new CustomEvent('connected', { detail: event }));
    }
    
    /**
     * Handle WebSocket message event
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            
            // Handle system messages
            if (message.type === 'pong') {
                this.clearPingTimeout();
                return;
            }
            
            if (message.type === 'welcome') {
                console.log('WebSocket welcome message:', message);
                return;
            }
            
            // Dispatch message event
            this.dispatchEvent(new CustomEvent('message', { 
                detail: { message, originalEvent: event }
            }));
            
            // Handle specific message types
            if (message.type === 'notification') {
                this.handleNotification(message);
            }
            
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    /**
     * Handle WebSocket close event
     */
    onClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        this.clearPingInterval();
        
        // Dispatch disconnected event
        this.dispatchEvent(new CustomEvent('disconnected', { detail: event }));
        
        // Schedule reconnect if enabled
        if (this.config.autoReconnect && event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * Handle WebSocket error event
     */
    onError(event) {
        console.error('WebSocket error:', event);
        
        // Dispatch error event
        this.dispatchEvent(new CustomEvent('error', { detail: event }));
    }
    
    /**
     * Handle notification messages
     */
    handleNotification(notification) {
        console.log('Received notification:', notification);
        
        // Dispatch notification event
        this.dispatchEvent(new CustomEvent('notification', { detail: notification }));
        
        // Show browser notification if supported and permitted
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/web/static/img/favicon.ico',
                tag: `odoo-notification-${notification.id}`,
                data: notification
            });
            
            browserNotification.onclick = () => {
                window.focus();
                this.markNotificationRead(notification.id);
                browserNotification.close();
            };
            
            // Auto-close after 5 seconds
            setTimeout(() => {
                browserNotification.close();
            }, 5000);
        }
    }
    
    /**
     * Start ping interval
     */
    startPingInterval() {
        this.clearPingInterval();
        
        this.pingInterval = setInterval(() => {
            this.ping();
        }, this.config.pingInterval);
    }
    
    /**
     * Clear ping interval
     */
    clearPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
        this.clearPingTimeout();
    }
    
    /**
     * Send ping message
     */
    ping() {
        if (this.isConnected) {
            this.send({ type: 'ping', timestamp: Date.now() });
            
            // Set timeout for pong response
            this.pingTimeout = setTimeout(() => {
                console.warn('Ping timeout, closing connection');
                this.websocket.close(1002, 'Ping timeout');
            }, this.config.pingTimeout);
        }
    }
    
    /**
     * Clear ping timeout
     */
    clearPingTimeout() {
        if (this.pingTimeout) {
            clearTimeout(this.pingTimeout);
            this.pingTimeout = null;
        }
    }
    
    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${this.reconnectDelay}ms`);
        
        setTimeout(() => {
            if (this.config.autoReconnect && !this.isConnected) {
                this.connect();
            }
        }, this.reconnectDelay);
        
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay);
    }
    
    /**
     * Request notification permission
     */
    static async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }
    
    /**
     * Get connection status
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions),
            queuedMessages: this.messageQueue.length
        };
    }
}

// Create global WebSocket client instance
const websocketClient = new OdooWebSocketClient();

// Auto-connect when the page loads
browser.addEventListener('DOMContentLoaded', () => {
    if (session.session_id && session.db) {
        websocketClient.connect();
    }
});

// Disconnect when page unloads
browser.addEventListener('beforeunload', () => {
    websocketClient.disconnect();
});

// Export for use in other modules
export { OdooWebSocketClient, websocketClient };

// Register as a service
registry.category("services").add("websocket", {
    start() {
        return websocketClient;
    },
});

// Register notification service
registry.category("services").add("websocket_notification", {
    dependencies: ["websocket", "notification"],
    start(_env, { websocket, notification }) {
        return new WebSocketNotificationService(websocket, notification);
    },
});

/**
 * WebSocket Notification Service
 * Integrates WebSocket notifications with Odoo's notification system
 */
class WebSocketNotificationService {
    constructor(websocketClient, notificationService) {
        this.websocket = websocketClient;
        this.notification = notificationService;

        // Listen for WebSocket notifications
        this.websocket.addEventListener('notification', this.handleNotification.bind(this));
    }

    handleNotification(event) {
        const notification = event.detail;

        // Show notification using Odoo's notification service
        const options = {
            type: notification.notification_type || 'info',
            sticky: notification.priority === 'urgent',
            className: `o_websocket_notification o_notification_${notification.notification_type}`,
        };

        this.notification.add(notification.message, options);

        // Mark as read after showing
        setTimeout(() => {
            this.websocket.markNotificationRead(notification.id);
        }, 1000);
    }
}
