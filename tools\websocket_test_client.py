#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket Test Client for Odoo

This script provides a simple test client for testing WebSocket functionality
outside of the web browser environment.
"""

import asyncio
import json
import logging
import sys
import argparse
from datetime import datetime

try:
    import websockets
except ImportError:
    print("Error: websockets library not installed. Run: pip install websockets")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OdooWebSocketTestClient:
    """
    Test client for Odoo WebSocket functionality
    """
    
    def __init__(self, host='localhost', port=8069, ssl=False):
        self.host = host
        self.port = port
        self.ssl = ssl
        self.websocket = None
        self.running = False
        
    async def connect(self, session_id, db_name):
        """Connect to Odoo WebSocket server"""
        protocol = 'wss' if self.ssl else 'ws'
        url = f"{protocol}://{self.host}:{self.port}/websocket?session_id={session_id}&db={db_name}"
        
        logger.info(f"Connecting to {url}")
        
        try:
            self.websocket = await websockets.connect(url)
            self.running = True
            logger.info("Connected successfully")
            return True
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket server"""
        if self.websocket:
            self.running = False
            await self.websocket.close()
            logger.info("Disconnected")
    
    async def send_message(self, message):
        """Send a message to the server"""
        if not self.websocket:
            logger.error("Not connected")
            return False
        
        try:
            if isinstance(message, dict):
                message = json.dumps(message)
            
            await self.websocket.send(message)
            logger.info(f"Sent: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    async def listen(self):
        """Listen for messages from the server"""
        if not self.websocket:
            logger.error("Not connected")
            return
        
        logger.info("Listening for messages...")
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    logger.warning(f"Received non-JSON message: {message}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("Connection closed by server")
        except Exception as e:
            logger.error(f"Error while listening: {e}")
    
    async def handle_message(self, message):
        """Handle incoming messages"""
        msg_type = message.get('type', 'unknown')
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        if msg_type == 'welcome':
            logger.info(f"[{timestamp}] Welcome message: {message}")
        elif msg_type == 'pong':
            logger.info(f"[{timestamp}] Pong received")
        elif msg_type == 'notification':
            logger.info(f"[{timestamp}] Notification: {message.get('title')} - {message.get('message')}")
        elif msg_type == 'subscribed':
            logger.info(f"[{timestamp}] Subscribed to channel: {message.get('channel')}")
        elif msg_type == 'unsubscribed':
            logger.info(f"[{timestamp}] Unsubscribed from channel: {message.get('channel')}")
        elif msg_type == 'error':
            logger.error(f"[{timestamp}] Server error: {message.get('message')}")
        else:
            logger.info(f"[{timestamp}] Received: {message}")
    
    async def ping(self):
        """Send ping message"""
        await self.send_message({
            'type': 'ping',
            'timestamp': datetime.now().isoformat()
        })
    
    async def subscribe(self, channel):
        """Subscribe to a channel"""
        await self.send_message({
            'type': 'subscribe',
            'channel': channel
        })
    
    async def unsubscribe(self, channel):
        """Unsubscribe from a channel"""
        await self.send_message({
            'type': 'unsubscribe',
            'channel': channel
        })
    
    async def interactive_mode(self):
        """Interactive mode for manual testing"""
        logger.info("Entering interactive mode. Type 'help' for commands.")
        
        while self.running:
            try:
                command = input("> ").strip()
                
                if not command:
                    continue
                
                if command == 'help':
                    print("Available commands:")
                    print("  ping                    - Send ping message")
                    print("  subscribe <channel>     - Subscribe to channel")
                    print("  unsubscribe <channel>   - Unsubscribe from channel")
                    print("  send <json>             - Send custom JSON message")
                    print("  quit                    - Quit interactive mode")
                    print("  help                    - Show this help")
                
                elif command == 'ping':
                    await self.ping()
                
                elif command.startswith('subscribe '):
                    channel = command[10:].strip()
                    if channel:
                        await self.subscribe(channel)
                    else:
                        print("Usage: subscribe <channel>")
                
                elif command.startswith('unsubscribe '):
                    channel = command[12:].strip()
                    if channel:
                        await self.unsubscribe(channel)
                    else:
                        print("Usage: unsubscribe <channel>")
                
                elif command.startswith('send '):
                    json_str = command[5:].strip()
                    try:
                        message = json.loads(json_str)
                        await self.send_message(message)
                    except json.JSONDecodeError:
                        print("Invalid JSON format")
                
                elif command == 'quit':
                    break
                
                else:
                    print(f"Unknown command: {command}. Type 'help' for available commands.")
            
            except KeyboardInterrupt:
                break
            except EOFError:
                break
        
        logger.info("Exiting interactive mode")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Odoo WebSocket Test Client')
    parser.add_argument('--host', default='localhost', help='WebSocket server host')
    parser.add_argument('--port', type=int, default=8069, help='WebSocket server port')
    parser.add_argument('--ssl', action='store_true', help='Use SSL/TLS connection')
    parser.add_argument('--session-id', required=True, help='Odoo session ID')
    parser.add_argument('--db', required=True, help='Database name')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')
    parser.add_argument('--ping-test', action='store_true', help='Run ping test')
    parser.add_argument('--subscribe', help='Subscribe to channel')
    
    args = parser.parse_args()
    
    # Create client
    client = OdooWebSocketTestClient(args.host, args.port, args.ssl)
    
    # Connect
    if not await client.connect(args.session_id, args.db):
        return 1
    
    try:
        # Start listening in background
        listen_task = asyncio.create_task(client.listen())
        
        # Wait a moment for connection to stabilize
        await asyncio.sleep(1)
        
        # Run tests based on arguments
        if args.ping_test:
            logger.info("Running ping test...")
            await client.ping()
            await asyncio.sleep(2)
        
        if args.subscribe:
            logger.info(f"Subscribing to channel: {args.subscribe}")
            await client.subscribe(args.subscribe)
            await asyncio.sleep(1)
        
        if args.interactive:
            # Run interactive mode
            await client.interactive_mode()
        else:
            # Just listen for a while
            logger.info("Listening for 30 seconds...")
            await asyncio.sleep(30)
        
        # Cancel listening task
        listen_task.cancel()
        try:
            await listen_task
        except asyncio.CancelledError:
            pass
    
    finally:
        await client.disconnect()
    
    return 0


if __name__ == '__main__':
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
