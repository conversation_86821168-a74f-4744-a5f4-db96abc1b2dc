# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Odoo CLI Core Module

This module provides the core functionality for the Odoo CLI system,
including command registration, discovery, and execution.
"""

from .registry import CommandRegistry, Command
from .discovery import CommandDiscovery
from .help import HelpFormatter
from .exceptions import CLIError, CommandNotFoundError, KeyboardInterruptError

__all__ = [
    'CommandRegistry',
    'Command', 
    'CommandDiscovery',
    'HelpFormatter',
    'CLIError',
    'CommandNotFoundError',
    'KeyboardInterruptError',
]
