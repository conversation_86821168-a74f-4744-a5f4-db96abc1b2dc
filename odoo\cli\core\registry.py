# Part of Odoo. See LICENSE file for full copyright and licensing details.
"""
Command Registry

This module handles command registration and management for the Odoo CLI.
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Type, Optional, List
from contextlib import contextmanager

from .exceptions import CommandNotFoundError

_logger = logging.getLogger(__name__)


class CommandRegistry:
    """Registry for CLI commands."""

    def __init__(self):
        self._commands: Dict[str, Type['Command']] = {}

    def register(self, command_class: Type['Command']) -> None:
        """Register a command class."""
        name = command_class.name or command_class.__name__.lower()
        if name in self._commands:
            _logger.warning("Command '%s' is already registered, overriding", name)
        self._commands[name] = command_class
        _logger.debug("Registered command: %s", name)

    def get(self, name: str) -> Type['Command']:
        """Get a command class by name."""
        if name not in self._commands:
            raise CommandNotFoundError(name, list(self._commands.keys()))
        return self._commands[name]

    def list_commands(self) -> Dict[str, Type['Command']]:
        """Get all registered commands."""
        return self._commands.copy()

    def has_command(self, name: str) -> bool:
        """Check if a command is registered."""
        return name in self._commands


# Global command registry
commands = CommandRegistry()


class Command:
    """Enhanced base class for CLI commands with signal and exception handling."""

    name: Optional[str] = None

    def __init_subclass__(cls, **kwargs):
        """Automatically register subclasses."""
        super().__init_subclass__(**kwargs)
        cls.name = cls.name or cls.__name__.lower()
        commands.register(cls)

    def __init__(self):
        """Initialize command with default settings."""
        self._cleanup_functions: List[callable] = []
        self._signal_handlers_setup = False

    def run(self, args):
        """
        Run the command with the given arguments.

        This method should be implemented by subclasses.

        Args:
            args: List of command-line arguments
        """
        raise NotImplementedError("Subclasses must implement the run method")

    def execute(self, args):
        """
        Execute the command with proper signal handling and cleanup.

        This method wraps the run() method with signal handling,
        exception handling, and cleanup.

        Args:
            args: List of command-line arguments
        """
        from .signals import register_cleanup_function  # noqa: PLC0415
        from .exception_handler import exception_context  # noqa: PLC0415

        # Setup signal handlers
        self.setup_signal_handlers()

        # Register cleanup function
        register_cleanup_function(self.cleanup)

        # Execute with exception handling
        with exception_context(debug=self._is_debug_mode()) as handler:
            try:
                self.run(args)
            except KeyboardInterrupt:
                _logger.info("Command interrupted by user")
                raise
            finally:
                self.cleanup()

    def get_description(self) -> str:
        """Get the command description."""
        return (self.__doc__ or "").strip()

    def setup_signal_handlers(self):
        """
        Setup signal handlers for the command.

        This method can be overridden by subclasses to provide
        custom signal handling behavior.
        """
        if not self._signal_handlers_setup:
            from .signals import setup_default_signal_handlers  # noqa: PLC0415
            setup_default_signal_handlers()
            self._signal_handlers_setup = True

    def register_cleanup(self, func: callable):
        """
        Register a cleanup function to be called when the command exits.

        Args:
            func: Cleanup function to register
        """
        self._cleanup_functions.append(func)

    def cleanup(self):
        """
        Cleanup resources when the command exits.

        This method can be overridden by subclasses to provide
        custom cleanup behavior.
        """
        for cleanup_func in self._cleanup_functions:
            try:
                cleanup_func()
            except Exception as e:
                _logger.error("Error in cleanup function %s: %s", cleanup_func, e)

    def _is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        # Check for common debug flags
        return any(arg in ['--debug', '-d', '--verbose', '-v'] for arg in sys.argv)

    def _get_prog_name(self) -> str:
        """Get the program name for help text."""
        return f'{Path(sys.argv[0]).name} {self.name}'
