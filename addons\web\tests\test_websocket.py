# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Tests for Odoo

This module contains tests for the WebSocket functionality,
including connection management, authentication, and notifications.
"""

import json
import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock

from odoo.tests.common import TransactionCase, HttpCase
from odoo.exceptions import AccessError


class TestWebSocketNotification(TransactionCase):
    """Test WebSocket notification model"""
    
    def setUp(self):
        super().setUp()
        self.notification_model = self.env['websocket.notification']
        self.user = self.env.ref('base.user_demo')
    
    def test_create_notification(self):
        """Test creating a WebSocket notification"""
        notification = self.notification_model.create_notification(
            title='Test Notification',
            message='This is a test message',
            notification_type='info',
            target_type='user',
            target_users=[self.user.id]
        )
        
        self.assertEqual(notification.title, 'Test Notification')
        self.assertEqual(notification.message, 'This is a test message')
        self.assertEqual(notification.notification_type, 'info')
        self.assertEqual(notification.target_type, 'user')
        self.assertIn(self.user.id, notification.target_user_ids.ids)
    
    def test_notification_expiry(self):
        """Test notification expiry functionality"""
        notification = self.notification_model.create_notification(
            title='Expiring Notification',
            message='This notification will expire',
            expires_hours=1
        )
        
        self.assertTrue(notification.expires_at)
        self.assertTrue(notification.auto_delete)
    
    def test_mark_as_read(self):
        """Test marking notification as read"""
        notification = self.notification_model.create_notification(
            title='Read Test',
            message='Test read functionality',
            target_type='user',
            target_users=[self.user.id]
        )
        
        # Mark as read
        notification.mark_as_read(self.user.id)
        
        # Check read record was created
        read_record = self.env['websocket.notification.read'].search([
            ('notification_id', '=', notification.id),
            ('user_id', '=', self.user.id)
        ])
        
        self.assertTrue(read_record)
    
    def test_user_notification_methods(self):
        """Test user notification helper methods"""
        # Test sending notification to user
        notification = self.user.send_websocket_notification(
            title='User Notification',
            message='Direct user notification',
            notification_type='success'
        )
        
        self.assertEqual(notification.target_type, 'user')
        self.assertIn(self.user.id, notification.target_user_ids.ids)
        
        # Test getting unread notifications
        unread = self.user.get_unread_websocket_notifications()
        self.assertIn(notification, unread)


class TestWebSocketAuthentication(TransactionCase):
    """Test WebSocket authentication"""
    
    def setUp(self):
        super().setUp()
        from odoo.service.websocket_auth import WebSocketAuthenticator
        self.authenticator = WebSocketAuthenticator()
    
    def test_token_generation(self):
        """Test JWT token generation and validation"""
        user_id = self.env.user.id
        db_name = self.env.cr.dbname
        
        # Generate token
        token = self.authenticator.generate_websocket_token(user_id, db_name)
        self.assertTrue(token)
        
        # Validate token
        payload = self.authenticator.token_manager.validate_token(token)
        self.assertEqual(payload['user_id'], user_id)
        self.assertEqual(payload['db_name'], db_name)
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        rate_limiter = self.authenticator.rate_limiter
        client_ip = '*************'
        
        # Should allow initial requests
        self.assertTrue(rate_limiter.check_rate_limit(client_ip))
        
        # Simulate failed attempts
        for _ in range(15):  # Exceed max attempts
            rate_limiter.record_failed_attempt(client_ip)
        
        # Should be rate limited now
        self.assertFalse(rate_limiter.check_rate_limit(client_ip))


class TestWebSocketConfig(unittest.TestCase):
    """Test WebSocket configuration"""
    
    def test_config_validation(self):
        """Test configuration validation"""
        from odoo.tools.websocket_config import WebSocketConfig
        
        config = WebSocketConfig()
        is_valid, errors, warnings = config.validate_config()
        
        # Should be valid with default settings
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_config_getters(self):
        """Test configuration getter methods"""
        from odoo.tools.websocket_config import get_websocket_config
        
        config = get_websocket_config()
        
        # Test various getters
        self.assertIsInstance(config.get_connection_limits(), dict)
        self.assertIsInstance(config.get_timeout_settings(), dict)
        self.assertIsInstance(config.get_auth_settings(), dict)
        self.assertIsInstance(config.is_enabled(), bool)


class TestWebSocketConnectionManager(unittest.TestCase):
    """Test WebSocket connection manager"""
    
    def setUp(self):
        from addons.web.controllers.websocket import WebSocketConnectionManager
        self.manager = WebSocketConnectionManager()
    
    @patch('starlette.websockets.WebSocket')
    async def test_connection_management(self, mock_websocket):
        """Test connection management"""
        websocket = mock_websocket()
        websocket.accept = AsyncMock()
        
        user_id = 1
        db_name = 'test_db'
        
        # Test connection
        await self.manager.connect(websocket, user_id, db_name)
        
        self.assertIn(user_id, self.manager.active_connections)
        self.assertIn(websocket, self.manager.active_connections[user_id])
        self.assertIn(websocket, self.manager.connection_metadata)
        
        # Test disconnection
        await self.manager.disconnect(websocket)
        
        self.assertNotIn(websocket, self.manager.connection_metadata)
    
    @patch('starlette.websockets.WebSocket')
    async def test_message_sending(self, mock_websocket):
        """Test message sending"""
        websocket = mock_websocket()
        websocket.accept = AsyncMock()
        websocket.send_text = AsyncMock()
        
        user_id = 1
        db_name = 'test_db'
        message = 'test message'
        
        # Connect websocket
        await self.manager.connect(websocket, user_id, db_name)
        
        # Send personal message
        await self.manager.send_personal_message(message, user_id)
        
        websocket.send_text.assert_called_with(message)
        
        # Test broadcast
        await self.manager.broadcast(message, db_name)
        
        # Should be called twice now (personal + broadcast)
        self.assertEqual(websocket.send_text.call_count, 2)


class TestWebSocketIntegration(HttpCase):
    """Integration tests for WebSocket functionality"""
    
    def test_websocket_endpoint_exists(self):
        """Test that WebSocket endpoint is accessible"""
        # This is a basic test to ensure the endpoint exists
        # Full WebSocket testing would require more complex setup
        
        # Test that the route is registered
        from odoo.asgi import asgi_root
        app = asgi_root.starlette_app
        
        # Check if WebSocket routes are registered
        websocket_routes = [route for route in app.routes if hasattr(route, 'path') and 'websocket' in route.path]
        self.assertTrue(len(websocket_routes) > 0)
    
    def test_notification_model_integration(self):
        """Test notification model integration"""
        # Create a notification
        notification = self.env['websocket.notification'].create({
            'title': 'Integration Test',
            'message': 'Testing integration',
            'notification_type': 'info',
            'target_type': 'all'
        })
        
        self.assertEqual(notification.state, 'draft')
        
        # Test sending (would normally trigger WebSocket)
        with patch('asyncio.create_task'):
            notification.send_notification()
        
        self.assertEqual(notification.state, 'sent')


class TestWebSocketJavaScript(unittest.TestCase):
    """Test JavaScript WebSocket client (mock tests)"""
    
    def test_client_structure(self):
        """Test that the JavaScript client file exists and has expected structure"""
        import os
        
        js_file_path = 'addons/web/static/src/js/websocket_client.js'
        self.assertTrue(os.path.exists(js_file_path))
        
        # Read file and check for key components
        with open(js_file_path, 'r') as f:
            content = f.read()
        
        # Check for key classes and functions
        self.assertIn('OdooWebSocketClient', content)
        self.assertIn('WebSocketNotificationService', content)
        self.assertIn('connect()', content)
        self.assertIn('disconnect()', content)
        self.assertIn('send(', content)
        self.assertIn('subscribe(', content)


# Test utilities
def create_mock_websocket():
    """Create a mock WebSocket for testing"""
    mock_ws = Mock()
    mock_ws.accept = AsyncMock()
    mock_ws.send_text = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.query_params = {}
    mock_ws.headers = {}
    mock_ws.client = Mock()
    mock_ws.client.host = '127.0.0.1'
    return mock_ws


def run_async_test(coro):
    """Helper to run async tests"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


if __name__ == '__main__':
    unittest.main()
