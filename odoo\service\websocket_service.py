# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Service for Odoo

This module provides WebSocket services for real-time communication,
including connection management, broadcasting, and integration with
Odoo's notification system.
"""

import json
import logging
import asyncio
from typing import Dict, Set, Optional, Any, List
from datetime import datetime, timedelta
from collections import defaultdict

from starlette.websockets import WebSocket

import odoo
from odoo import api, registry
from odoo.tools import config

_logger = logging.getLogger(__name__)


class WebSocketNotificationService:
    """
    Service for handling real-time notifications through WebSocket
    """
    
    def __init__(self, connection_manager):
        self.connection_manager = connection_manager
        self._notification_queue = asyncio.Queue()
        self._running = False
        self._task = None
    
    async def start(self):
        """Start the notification service"""
        if not self._running:
            self._running = True
            self._task = asyncio.create_task(self._process_notifications())
            _logger.info("WebSocket notification service started")
    
    async def stop(self):
        """Stop the notification service"""
        if self._running:
            self._running = False
            if self._task:
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass
            _logger.info("WebSocket notification service stopped")
    
    async def _process_notifications(self):
        """Process queued notifications"""
        while self._running:
            try:
                notification = await asyncio.wait_for(
                    self._notification_queue.get(), 
                    timeout=1.0
                )
                await self._send_notification(notification)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                _logger.error(f"Error processing notification: {e}")
    
    async def _send_notification(self, notification: dict):
        """Send a notification to appropriate WebSocket connections"""
        notification_type = notification.get('type')
        target_users = notification.get('target_users', [])
        db_name = notification.get('db_name')
        message = notification.get('message', {})
        
        if notification_type == 'user_specific':
            # Send to specific users
            for user_id in target_users:
                await self.connection_manager.send_personal_message(
                    json.dumps(message), user_id
                )
        elif notification_type == 'broadcast':
            # Broadcast to all users in database
            await self.connection_manager.broadcast(
                json.dumps(message), db_name
            )
        elif notification_type == 'channel':
            # Send to users subscribed to a specific channel
            channel = notification.get('channel')
            await self._send_to_channel(channel, message, db_name)
    
    async def _send_to_channel(self, channel: str, message: dict, db_name: str = None):
        """Send message to all connections subscribed to a channel"""
        connections_to_send = []
        
        async with self.connection_manager._lock:
            for connections in self.connection_manager.active_connections.values():
                for connection in connections:
                    metadata = self.connection_manager.connection_metadata.get(connection)
                    if metadata and (db_name is None or metadata['db_name'] == db_name):
                        subscriptions = metadata.get('subscriptions', set())
                        if channel in subscriptions:
                            connections_to_send.append(connection)
        
        for connection in connections_to_send:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                _logger.warning(f"Failed to send channel message: {e}")
                await self.connection_manager.disconnect(connection)
    
    async def queue_notification(self, notification: dict):
        """Queue a notification for processing"""
        await self._notification_queue.put(notification)
    
    async def send_user_notification(self, user_ids: List[int], message: dict, db_name: str):
        """Send notification to specific users"""
        await self.queue_notification({
            'type': 'user_specific',
            'target_users': user_ids,
            'db_name': db_name,
            'message': message
        })
    
    async def send_broadcast_notification(self, message: dict, db_name: str = None):
        """Send broadcast notification"""
        await self.queue_notification({
            'type': 'broadcast',
            'db_name': db_name,
            'message': message
        })
    
    async def send_channel_notification(self, channel: str, message: dict, db_name: str = None):
        """Send notification to channel subscribers"""
        await self.queue_notification({
            'type': 'channel',
            'channel': channel,
            'db_name': db_name,
            'message': message
        })


class WebSocketMetricsService:
    """
    Service for collecting WebSocket metrics and statistics
    """
    
    def __init__(self, connection_manager):
        self.connection_manager = connection_manager
        self.metrics = {
            'total_connections': 0,
            'total_messages_sent': 0,
            'total_messages_received': 0,
            'connection_errors': 0,
            'peak_connections': 0,
            'connections_by_db': defaultdict(int),
            'uptime_start': datetime.now()
        }
        self._metrics_lock = asyncio.Lock()
    
    async def record_connection(self, db_name: str):
        """Record a new connection"""
        async with self._metrics_lock:
            self.metrics['total_connections'] += 1
            self.metrics['connections_by_db'][db_name] += 1
            
            current_connections = self.connection_manager.get_total_connections_count()
            if current_connections > self.metrics['peak_connections']:
                self.metrics['peak_connections'] = current_connections
    
    async def record_disconnection(self, db_name: str):
        """Record a disconnection"""
        async with self._metrics_lock:
            if self.metrics['connections_by_db'][db_name] > 0:
                self.metrics['connections_by_db'][db_name] -= 1
    
    async def record_message_sent(self):
        """Record a sent message"""
        async with self._metrics_lock:
            self.metrics['total_messages_sent'] += 1
    
    async def record_message_received(self):
        """Record a received message"""
        async with self._metrics_lock:
            self.metrics['total_messages_received'] += 1
    
    async def record_error(self):
        """Record an error"""
        async with self._metrics_lock:
            self.metrics['connection_errors'] += 1
    
    async def get_metrics(self) -> dict:
        """Get current metrics"""
        async with self._metrics_lock:
            current_connections = self.connection_manager.get_total_connections_count()
            uptime = datetime.now() - self.metrics['uptime_start']
            
            return {
                **self.metrics,
                'current_connections': current_connections,
                'uptime_seconds': uptime.total_seconds(),
                'connections_by_db': dict(self.metrics['connections_by_db'])
            }


class WebSocketCleanupService:
    """
    Service for cleaning up stale WebSocket connections
    """
    
    def __init__(self, connection_manager):
        self.connection_manager = connection_manager
        self._cleanup_interval = 300  # 5 minutes
        self._max_idle_time = 1800  # 30 minutes
        self._running = False
        self._task = None
    
    async def start(self):
        """Start the cleanup service"""
        if not self._running:
            self._running = True
            self._task = asyncio.create_task(self._cleanup_loop())
            _logger.info("WebSocket cleanup service started")
    
    async def stop(self):
        """Stop the cleanup service"""
        if self._running:
            self._running = False
            if self._task:
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass
            _logger.info("WebSocket cleanup service stopped")
    
    async def _cleanup_loop(self):
        """Main cleanup loop"""
        while self._running:
            try:
                await self._cleanup_stale_connections()
                await asyncio.sleep(self._cleanup_interval)
            except Exception as e:
                _logger.error(f"Error in WebSocket cleanup: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    async def _cleanup_stale_connections(self):
        """Clean up stale connections"""
        now = datetime.now()
        stale_connections = []
        
        async with self.connection_manager._lock:
            for connection, metadata in self.connection_manager.connection_metadata.items():
                last_activity = metadata.get('last_activity', now)
                if (now - last_activity).total_seconds() > self._max_idle_time:
                    stale_connections.append(connection)
        
        for connection in stale_connections:
            try:
                await connection.close(code=1001, reason="Connection idle timeout")
            except:
                pass
            await self.connection_manager.disconnect(connection)
        
        if stale_connections:
            _logger.info(f"Cleaned up {len(stale_connections)} stale WebSocket connections")


class WebSocketServiceManager:
    """
    Main service manager for WebSocket functionality
    """
    
    def __init__(self):
        # Import here to avoid circular imports
        from odoo.addons.web.controllers.websocket import connection_manager
        
        self.connection_manager = connection_manager
        self.notification_service = WebSocketNotificationService(connection_manager)
        self.metrics_service = WebSocketMetricsService(connection_manager)
        self.cleanup_service = WebSocketCleanupService(connection_manager)
        self._started = False
    
    async def start(self):
        """Start all WebSocket services"""
        if not self._started:
            await self.notification_service.start()
            await self.cleanup_service.start()
            self._started = True
            _logger.info("WebSocket service manager started")
    
    async def stop(self):
        """Stop all WebSocket services"""
        if self._started:
            await self.notification_service.stop()
            await self.cleanup_service.stop()
            self._started = False
            _logger.info("WebSocket service manager stopped")
    
    def get_connection_manager(self):
        """Get the connection manager"""
        return self.connection_manager
    
    def get_notification_service(self):
        """Get the notification service"""
        return self.notification_service
    
    def get_metrics_service(self):
        """Get the metrics service"""
        return self.metrics_service


# Global service manager instance
websocket_service_manager = WebSocketServiceManager()


def get_websocket_service_manager():
    """Get the global WebSocket service manager"""
    return websocket_service_manager
