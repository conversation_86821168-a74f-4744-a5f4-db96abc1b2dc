# Odoo Interactive Runner

A Python script that runs odoo-bin with interactive controls for easy management during development.

## Features

- 🚀 **Start Odoo**: Automatically runs odoo-bin with your specified arguments
- 🔄 **Restart**: Press 'r' to restart Odoo without stopping the runner
- 🛑 **Graceful Exit**: Press 'x' or Ctrl+C for clean shutdown
- 📊 **Status Check**: Press 's' to see current process status
- 🔥 **Force Kill**: Press 'k' to force kill unresponsive processes
- 📋 **Help**: Press 'h' to see all available commands
- 📺 **Live Output**: See Odoo logs in real-time

## Usage

### Basic Usage
```bash
python odoo_runner.py
```

### With Odoo Arguments
```bash
python odoo_runner.py --dev=all --log-level=debug
python odoo_runner.py -d mydb -i base --without-demo=all
```

### Windows Batch File
```cmd
run_odoo_interactive.bat --dev=all
```

## Interactive Controls

While the script is running, you can use these single-key commands:

| Key | Action |
|-----|--------|
| `r` | Restart Odoo |
| `x` | Exit runner |
| `s` | Show status |
| `k` | Force kill Odoo |
| `h` | Show help |
| `Ctrl+C` | Graceful shutdown |

## Requirements

- Python 3.6+
- Odoo installation with odoo-bin in the current directory
- Unix-like terminal (Linux/macOS) or Windows Command Prompt

## Examples

### Development Mode
```bash
python odoo_runner.py --dev=all --log-level=debug
```

### Database Operations
```bash
python odoo_runner.py -d mydb -i sale,purchase --stop-after-init
```

### Custom Configuration
```bash
python odoo_runner.py -c custom.conf --dev=reload
```

## Troubleshooting

### Terminal Issues
If you experience terminal input issues, the script includes fallback mechanisms for different environments.

### Process Not Stopping
Use the 'k' command to force kill unresponsive processes.

### Permission Issues
Ensure odoo-bin is executable:
```bash
chmod +x odoo-bin
```

## Notes

- The script automatically detects odoo-bin in the current directory
- All Odoo output is displayed in real-time
- Process management is handled gracefully with proper cleanup
- Works in both interactive and non-interactive environments
