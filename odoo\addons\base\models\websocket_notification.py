# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Real-time Notification System

This module extends Odoo's notification system to support real-time
WebSocket notifications for immediate delivery to connected clients.
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional

from odoo import api, fields, models, tools
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WebSocketNotification(models.Model):
    """
    Model for managing WebSocket notifications
    """
    _name = 'websocket.notification'
    _description = 'WebSocket Notification'
    _order = 'create_date desc'
    _rec_name = 'title'
    
    title = fields.Char('Title', required=True)
    message = fields.Text('Message', required=True)
    notification_type = fields.Selection([
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('success', 'Success'),
        ('custom', 'Custom')
    ], string='Type', default='info', required=True)
    
    # Target specification
    target_type = fields.Selection([
        ('user', 'Specific Users'),
        ('group', 'User Groups'),
        ('all', 'All Users'),
        ('channel', 'Channel Subscribers')
    ], string='Target Type', default='user', required=True)
    
    target_user_ids = fields.Many2many('res.users', string='Target Users')
    target_group_ids = fields.Many2many('res.groups', string='Target Groups')
    channel_name = fields.Char('Channel Name')
    
    # Notification data
    data = fields.Text('Additional Data (JSON)')
    priority = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent')
    ], string='Priority', default='normal')
    
    # Status tracking
    state = fields.Selection([
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('failed', 'Failed')
    ], string='State', default='draft')
    
    sent_date = fields.Datetime('Sent Date')
    error_message = fields.Text('Error Message')
    
    # Delivery tracking
    delivery_count = fields.Integer('Delivery Count', default=0)
    read_count = fields.Integer('Read Count', default=0)
    
    # Auto-cleanup
    expires_at = fields.Datetime('Expires At')
    auto_delete = fields.Boolean('Auto Delete', default=True)
    
    @api.model
    def create_notification(self, title, message, notification_type='info', 
                          target_type='user', target_users=None, target_groups=None,
                          channel_name=None, data=None, priority='normal', 
                          expires_hours=24):
        """
        Create and send a WebSocket notification
        """
        vals = {
            'title': title,
            'message': message,
            'notification_type': notification_type,
            'target_type': target_type,
            'priority': priority,
            'data': json.dumps(data) if data else None,
            'channel_name': channel_name,
        }
        
        if expires_hours:
            vals['expires_at'] = fields.Datetime.now() + tools.timedelta(hours=expires_hours)
        
        notification = self.create(vals)
        
        # Set target users/groups
        if target_users:
            notification.target_user_ids = [(6, 0, target_users)]
        if target_groups:
            notification.target_group_ids = [(6, 0, target_groups)]
        
        # Send the notification
        notification.send_notification()
        
        return notification
    
    def send_notification(self):
        """
        Send the notification via WebSocket
        """
        self.ensure_one()
        
        try:
            # Get WebSocket service manager
            from odoo.service.websocket_service import get_websocket_service_manager
            service_manager = get_websocket_service_manager()
            notification_service = service_manager.get_notification_service()
            
            # Prepare notification message
            message = {
                'id': self.id,
                'type': 'notification',
                'notification_type': self.notification_type,
                'title': self.title,
                'message': self.message,
                'priority': self.priority,
                'timestamp': fields.Datetime.to_string(fields.Datetime.now()),
                'data': json.loads(self.data) if self.data else {}
            }
            
            # Send based on target type
            if self.target_type == 'user':
                user_ids = self.target_user_ids.ids
                if user_ids:
                    asyncio.create_task(
                        notification_service.send_user_notification(
                            user_ids, message, self.env.cr.dbname
                        )
                    )
            
            elif self.target_type == 'group':
                # Get all users in target groups
                user_ids = self.target_group_ids.mapped('users.id')
                if user_ids:
                    asyncio.create_task(
                        notification_service.send_user_notification(
                            user_ids, message, self.env.cr.dbname
                        )
                    )
            
            elif self.target_type == 'all':
                asyncio.create_task(
                    notification_service.send_broadcast_notification(
                        message, self.env.cr.dbname
                    )
                )
            
            elif self.target_type == 'channel':
                if self.channel_name:
                    asyncio.create_task(
                        notification_service.send_channel_notification(
                            self.channel_name, message, self.env.cr.dbname
                        )
                    )
            
            # Update notification status
            self.write({
                'state': 'sent',
                'sent_date': fields.Datetime.now()
            })
            
        except Exception as e:
            _logger.error(f"Failed to send WebSocket notification {self.id}: {e}")
            self.write({
                'state': 'failed',
                'error_message': str(e)
            })
    
    def mark_as_read(self, user_id):
        """
        Mark notification as read by a user
        """
        self.ensure_one()
        
        # Create or update read record
        read_record = self.env['websocket.notification.read'].search([
            ('notification_id', '=', self.id),
            ('user_id', '=', user_id)
        ])
        
        if not read_record:
            self.env['websocket.notification.read'].create({
                'notification_id': self.id,
                'user_id': user_id,
                'read_date': fields.Datetime.now()
            })
            
            # Update read count
            self.read_count = len(self.read_ids)
    
    @api.model
    def cleanup_expired_notifications(self):
        """
        Clean up expired notifications
        """
        expired = self.search([
            ('expires_at', '<=', fields.Datetime.now()),
            ('auto_delete', '=', True)
        ])
        
        if expired:
            _logger.info(f"Cleaning up {len(expired)} expired WebSocket notifications")
            expired.unlink()


class WebSocketNotificationRead(models.Model):
    """
    Track which users have read which notifications
    """
    _name = 'websocket.notification.read'
    _description = 'WebSocket Notification Read Status'
    _rec_name = 'notification_id'
    
    notification_id = fields.Many2one('websocket.notification', 'Notification', required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', 'User', required=True, ondelete='cascade')
    read_date = fields.Datetime('Read Date', required=True)
    
    _sql_constraints = [
        ('unique_user_notification', 'unique(notification_id, user_id)', 
         'A user can only read a notification once.')
    ]


class ResUsers(models.Model):
    """
    Extend res.users to add WebSocket notification methods
    """
    _inherit = 'res.users'
    
    def send_websocket_notification(self, title, message, notification_type='info', 
                                  data=None, priority='normal', expires_hours=24):
        """
        Send a WebSocket notification to this user
        """
        return self.env['websocket.notification'].create_notification(
            title=title,
            message=message,
            notification_type=notification_type,
            target_type='user',
            target_users=self.ids,
            data=data,
            priority=priority,
            expires_hours=expires_hours
        )
    
    def get_unread_websocket_notifications(self):
        """
        Get unread WebSocket notifications for this user
        """
        # Get notifications targeted to this user
        user_notifications = self.env['websocket.notification'].search([
            ('target_type', '=', 'user'),
            ('target_user_ids', 'in', self.id),
            ('state', '=', 'sent')
        ])
        
        # Get notifications targeted to user's groups
        group_notifications = self.env['websocket.notification'].search([
            ('target_type', '=', 'group'),
            ('target_group_ids', 'in', self.groups_id.ids),
            ('state', '=', 'sent')
        ])
        
        # Get broadcast notifications
        broadcast_notifications = self.env['websocket.notification'].search([
            ('target_type', '=', 'all'),
            ('state', '=', 'sent')
        ])
        
        all_notifications = user_notifications | group_notifications | broadcast_notifications
        
        # Filter out read notifications
        read_notification_ids = self.env['websocket.notification.read'].search([
            ('user_id', '=', self.id),
            ('notification_id', 'in', all_notifications.ids)
        ]).mapped('notification_id.id')
        
        unread_notifications = all_notifications.filtered(
            lambda n: n.id not in read_notification_ids
        )
        
        return unread_notifications


class MailThread(models.AbstractModel):
    """
    Extend mail.thread to add WebSocket notification support
    """
    _inherit = 'mail.thread'
    
    def _notify_websocket(self, title, message, notification_type='info', 
                         target_users=None, data=None):
        """
        Send WebSocket notification related to this record
        """
        if not target_users:
            # Default to followers
            target_users = self.message_follower_ids.mapped('partner_id.user_ids')
        
        # Add record information to data
        record_data = {
            'model': self._name,
            'res_id': self.id,
            'record_name': self.display_name if hasattr(self, 'display_name') else str(self)
        }
        
        if data:
            record_data.update(data)
        
        return self.env['websocket.notification'].create_notification(
            title=title,
            message=message,
            notification_type=notification_type,
            target_type='user',
            target_users=target_users.ids,
            data=record_data
        )
